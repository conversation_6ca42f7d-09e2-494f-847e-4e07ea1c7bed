<!DOCTYPE html>
<html lang="vi">
  <head>
    <title>SayTruyenHot - Đ<PERSON><PERSON>n Chữ Online Nhanh Và Miễn <PERSON>í</title>
    <meta charset="utf-8" />
    <base href="/" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="Đọc truyện chữ online miễn phí tại SayTruyenHot. Hàng nghìn bộ truyện hay, cập nhật nhanh nhất. Thể loại đa dạng: ngôn tình, tiê<PERSON> hiệ<PERSON>, huy<PERSON><PERSON> huy<PERSON>, xuyên không." />
    <meta name="keywords" content="đọc truyện, truyện chữ, truyện online, truyện miễn <PERSON>h<PERSON>, ngô<PERSON> tình, tiên hi<PERSON><PERSON>, huy<PERSON><PERSON> h<PERSON>, x<PERSON><PERSON><PERSON> kh<PERSON>, SayTruyenHot" />
    <meta name="author" content="SayTruyenHot" />
    <meta name="copyright" content="Copyright © 2025 SayTruyenHot" />
    <meta name="robots" content="index, follow" />
    <meta name="google" content="notranslate" />
    <meta name="theme-color" content="#ffffff" />
    
    <!-- Open Graph Tags -->
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="SayTruyenHot" />
    <meta property="og:locale" content="vi_VN" />
    <meta property="og:image" content="/logo.png" />
    <meta property="og:image:width" content="480" />
    <meta property="og:image:height" content="480" />
    <meta property="og:image:alt" content="SayTruyenHot - Đọc truyện chữ online miễn phí" />
    
    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@saytruyenhot" />
    <meta name="twitter:creator" content="@saytruyenhot" />
    <meta name="twitter:image" content="/logo.png" />
    
    <!-- Canonical URL will be set dynamically by Angular -->
    <link rel="canonical" href="/" />

    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="preconnect" href="https://static.saytruyenhot.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preload" as="image" href="/logo.png">
    <link rel="preload" as="image" href="/Subtract.png">

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-11QMWS15HV"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-11QMWS15HV');
    </script>
  </head>

  <body>
    <app-root></app-root>
  </body>
</html>
