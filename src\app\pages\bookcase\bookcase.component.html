<div class="common-container">
  <app-breadcrumb class="flex py-2" [Links]="[
            { label: 'Trang chủ', url: '/' },
            { label: '<PERSON><PERSON> Sách', url: '/tu-sach' },
            { label: tabs[selectedTab].name, url: tabs[selectedTab].routerLink }
          ]">
  </app-breadcrumb>
  <app-tabs
    [tabs]="tabs"
    [selectedTab]="selectedTab"
    (selectedTabChange)="onTabChange($event)"
    class="flex mb-2"
  ></app-tabs>

  <router-outlet></router-outlet>
</div>
