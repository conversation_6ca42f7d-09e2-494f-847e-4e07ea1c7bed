<nav class="breadcrumb-nav" aria-label="Breadcrumb navigation">
  <ol class="breadcrumb-list" itemscope itemtype="https://schema.org/BreadcrumbList">
    <li 
      *ngFor="let link of Links; let i = index; let last = last" 
      class="breadcrumb-item"
      itemprop="itemListElement" 
      itemscope 
      itemtype="https://schema.org/ListItem"
    >
      <a
        *ngIf="!last && link.url; else textOnly"
        [ngClass]="{
          'breadcrumb-item-last': last
        }"
        class="breadcrumb-name"
        [routerLink]="[link.url]"
        itemprop="item"
      >
        <span itemprop="name">{{ link.label }}</span>
      </a>
      <ng-template #textOnly>
        <span class="breadcrumb-name breadcrumb-item-last" itemprop="name">{{ link.label }}</span>
      </ng-template>
      <meta itemprop="position" [content]="i + 1">
      
      <svg 
        *ngIf="!last"
        class="breadcrumb-divider" 
        xmlns="http://www.w3.org/2000/svg"
        fill="none" 
        viewBox="0 0 6 10"
        aria-hidden="true"
      >
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4" />
      </svg>
    </li>
  </ol>
</nav>
