import { CommonModule, isPlatformServer } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, Inject, PLATFORM_ID, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { AppSettingComponent } from '@components/app-setting/app-setting.component';
import { GenresComponent } from '@components/genres/genres.component';
import { SearchBarComponent } from '@components/search-bar/search-bar.component';
import { fadeInOut } from '@components/utils/animation';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { TutorialDirective } from '@directives/tutorial.directive';
import { LoginFormComponent } from '@pages/authentication/login-form/login-form.component';
import { RegisterFormComponent } from '@pages/authentication/register-form/register-form.component';
import { SettingType } from '@schemas/SettingType.enum';
import { User } from '@schemas/User';
import { AccountService } from '@services/account.service';
import { DynamicLoadingService } from '@services/dynamic.loading.service';
import { EventService } from '@services/event.service';
import { SettingService } from '@services/setting.service';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
  animations: [fadeInOut],
  imports: [CommonModule, RouterLink, SearchBarComponent, GenresComponent, ClickOutsideDirective, TutorialDirective],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,

})
export class NavbarComponent {

  showAccPopup = false;
  showMenuPopup = false
  headerSticky = false;
  showHeaderBg = false;
  genresVisible = false;

  constructor(
    public settingService: SettingService,
    private dynamicLoadingService: DynamicLoadingService,
    private accountService: AccountService,
    private eventService: EventService,
    private router: Router,
    private cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Object) { }

  @ViewChild('navbar') navbar: any | null = null;
  @ViewChild('themebtn') themebtn: any | null = null;

  ngOnInit() {
    this.accountService.currentUser$.subscribe(user => this.curUser = user);
    this.settingService.OnSettingChange(SettingType.FixedHeader).subscribe((setting) => {
      this.headerSticky = setting.value == true;
    });
    this.settingService.OnSettingChange(SettingType.Theme).subscribe(theme => {

    }
    );
  }

  toggleTheme() {
    this.themebtn?.nativeElement.classList.toggle('dark-mode');
    this.settingService.IsDarkTheme() ? this.settingService.Set(SettingType.Theme, '') : this.settingService.Set(SettingType.Theme, 'dark');
  }
  @HostListener('window:scroll', [])
  onWindowScroll() {
    this.showHeaderBg = window.scrollY > 0 && this.headerSticky;
  }

  ngAfterViewInit() {
    // timer(0).subscribe(() => this.openSetting());



  }


  curUser?: User
  toggleAccount() {
    this.showAccPopup = !this.showAccPopup;
  }
  toggleMenue() {
    console.log(this.showMenuPopup);

    this.showMenuPopup = !this.showMenuPopup
  }
  register() {
    this.showAccPopup = false;
    let component = this.dynamicLoadingService.createDynamicComponent<RegisterFormComponent>(RegisterFormComponent)
    component?.instance.toggle();
  }
  login() {
    this.showAccPopup = false;
    let component1 = this.dynamicLoadingService.createDynamicComponent<LoginFormComponent>(LoginFormComponent)
    component1?.instance.toggle();
  }

  logout() {
    this.showAccPopup = false;
    this.accountService.Logout();
    window.location.reload();
  }
  openSetting() {
    this.showAccPopup = false;
    let component1 = this.dynamicLoadingService.createDynamicComponent<AppSettingComponent>(AppSettingComponent)
    component1?.instance.open();
  }
  goToVip() {
    this.router.navigate(['/price/vip']);
    this.showAccPopup = false;
  }

  goToCoin() {
    this.router.navigate(['/price/coin']);
    this.showAccPopup = false;
  }

  goToBookcase() {
    this.router.navigate(['/tu-sach/theo-doi']);
    this.showAccPopup = false;

  }

  isServer() {
    return isPlatformServer(this.platformId);
  }
  goToProfile() {
    this.router.navigate(['/tai-khoan']);
    this.showAccPopup = false;

  }
}
