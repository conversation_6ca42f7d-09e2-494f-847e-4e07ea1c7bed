<div class="common-container">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner"></div>
    <p><PERSON><PERSON> tải thông tin...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading && user" class="user-page-content">
    <!-- Header with User Info -->
    <div class="user-header">
      <div class="user-avatar">
        <img 
          [src]="user.avatar || '/default_avatar.jpeg'" 
          [alt]="user.firstName + ' ' + user.lastName"
          class="avatar-image"
          loading="lazy"
        />
      </div>
      <div class="user-info">
        <h1 class="user-name">{{ user.firstName }} {{ user.lastName }}</h1>
        <p class="user-email">{{ user.email }}</p>
        <div class="user-stats">
          <div class="stat-item">
            <span class="stat-label">Thành viên từ</span>
            <span class="stat-value">{{ transform(user.createAt) }}</span>
          </div>
          <div class="stat-item vip-status" [class.active]="user.vip">
            <svg class="vip-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M5 16L3 5h5.5l1.5 5 1.5-5H16l-2 11h-4l-1.5-5L7 16H5z"/>
            </svg>
            <span *ngIf="user.vip; else freeStatus">VIP - {{ transform(user.vipExpireAat) }}</span>
            <ng-template #freeStatus>
              <span>Miễn phí</span>
            </ng-template>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <button 
        class="tab-button"
        [class.active]="activeTab === 'profile'"
        (click)="switchTab('profile')"
      >
        <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
        <span>Hồ sơ</span>
      </button>

      <button 
        class="tab-button"
        [class.active]="activeTab === 'bookcase'"
        (click)="switchTab('bookcase')"
      >
        <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
        </svg>
        <span>Tủ sách</span>
      </button>

      <button 
        class="tab-button"
        [class.active]="activeTab === 'settings'"
        (click)="switchTab('settings')"
      >
        <svg class="tab-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        <span>Cài đặt</span>
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <div *ngIf="activeTab === 'profile'" class="tab-panel">
        <app-profile-info [user]="user"></app-profile-info>
      </div>

      <div *ngIf="activeTab === 'bookcase'" class="tab-panel">
        <app-bookcase></app-bookcase>
      </div>

      <div *ngIf="activeTab === 'settings'" class="tab-panel">
        <app-user-settings></app-user-settings>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="!isLoading && !user" class="error-container">
    <svg class="error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 18.5c-.77.833.192 2.5 1.732 2.5z"></path>
    </svg>
    <h3>Không thể tải thông tin tài khoản</h3>
    <p>Vui lòng thử lại sau hoặc liên hệ hỗ trợ.</p>
    <button class="retry-button" (click)="loadUserData()">Thử lại</button>
  </div>
</div>
