import { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';
import { CommentModule } from '@components/comment/comment.module';

@Component({
  selector: 'app-icon-author',
  standalone: true,
  templateUrl: './author.component.html',
  styleUrl: './author.component.scss',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommentModule],
})
export class AuthorIconComponent {
}
