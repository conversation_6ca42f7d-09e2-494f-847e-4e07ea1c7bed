.landing-page {
  @apply min-h-screen bg-gray-50 dark:bg-dark-background;
}

// Hero Section
.hero-section {
  @apply bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white py-16 lg:py-24;
}

.hero-content {
  @apply max-w-6xl mx-auto px-4 text-center;
}

.hero-title {
  @apply text-3xl lg:text-5xl font-bold mb-6 leading-tight;
}

.hero-highlight {
  @apply block text-yellow-300 mt-2;
}

.hero-description {
  @apply text-lg lg:text-xl mb-8 max-w-4xl mx-auto leading-relaxed opacity-90;
  
  strong {
    @apply text-yellow-300 font-semibold;
  }
}

.hero-stats {
  @apply flex justify-center gap-8 mb-8 flex-wrap;
}

.stat-item {
  @apply text-center;
}

.stat-number {
  @apply block text-2xl lg:text-3xl font-bold text-yellow-300;
}

.stat-label {
  @apply block text-sm lg:text-base opacity-80;
}

.hero-actions {
  @apply flex justify-center gap-4 flex-wrap;
}

// Buttons
.btn-primary {
  @apply bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold px-8 py-3 rounded-lg transition-colors duration-200 inline-block;
}

.btn-secondary {
  @apply bg-transparent border-2 border-white hover:bg-white hover:text-gray-900 text-white font-semibold px-8 py-3 rounded-lg transition-colors duration-200 inline-block;
}

// Sections
.section-header {
  @apply text-center mb-12;
}

.section-title {
  @apply text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-4;
}

.section-description {
  @apply text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto;
}

// Genres Section
.genres-section {
  @apply py-16 max-w-6xl mx-auto px-4;
}

.genres-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.genre-card {
  @apply bg-white dark:bg-dark-700 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300;
}

.genre-link {
  @apply block p-6 h-full hover:no-underline;
}

.genre-name {
  @apply text-xl font-bold text-gray-900 dark:text-white mb-2;
}

.genre-description {
  @apply text-gray-600 dark:text-gray-300 mb-4;
}

.genre-arrow {
  @apply text-blue-600 dark:text-blue-400 font-bold text-lg;
}

// Featured Section
.featured-section {
  @apply py-16 bg-white dark:bg-dark-800;
}

.novels-grid {
  @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 max-w-6xl mx-auto px-4;
}

.section-footer {
  @apply text-center mt-12;
}

.view-more-link {
  @apply text-blue-600 dark:text-blue-400 hover:underline font-semibold text-lg;
}

// Benefits Section
.benefits-section {
  @apply py-16 max-w-6xl mx-auto px-4;
}

.benefits-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
}

.benefit-item {
  @apply text-center;
}

.benefit-icon {
  @apply text-4xl mb-4;
}

.benefit-title {
  @apply text-lg font-bold text-gray-900 dark:text-white mb-2;
}

.benefit-description {
  @apply text-gray-600 dark:text-gray-300 text-sm leading-relaxed;
}

// FAQ Section
.faq-wrapper {
  @apply py-16 bg-white dark:bg-dark-800;
}

// CTA Section
.cta-section {
  @apply py-16 bg-gradient-to-r from-indigo-600 to-purple-600 text-white;
}

.cta-content {
  @apply max-w-4xl mx-auto px-4 text-center;
}

.cta-title {
  @apply text-2xl lg:text-3xl font-bold mb-4;
}

.cta-description {
  @apply text-lg mb-8 opacity-90;
}

.cta-actions {
  @apply flex justify-center gap-4 flex-wrap;
}

// Responsive Design
@media (max-width: 640px) {
  .hero-title {
    @apply text-2xl;
  }
  
  .hero-description {
    @apply text-base;
  }
  
  .hero-stats {
    @apply gap-4;
  }
  
  .stat-number {
    @apply text-xl;
  }
  
  .btn-primary,
  .btn-secondary {
    @apply px-6 py-2 text-sm;
  }
  
  .novels-grid {
    @apply grid-cols-2 gap-3;
  }
  
  .benefits-grid {
    @apply grid-cols-1 gap-6;
  }
}
