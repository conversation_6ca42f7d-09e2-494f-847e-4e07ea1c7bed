import { NgModule } from '@angular/core';

import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from './layouts/app-layout/app-layout.component';
import { chapterResolver, listChapterResolver } from './resolvers/chapter.resolver';
import { novelResolver } from './resolvers/novel.resolver';
import { recommendResolver } from './resolvers/recommend.resolver';

const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      {
        path: '',
        resolve: {
          recommendRes: recommendResolver,
        },
        loadChildren: () =>
          import('./pages/home/<USER>').then((m) => m.HomeModule),
      },
      {
        path: 'auth',
        loadChildren: () =>
          import('./pages/auth/auth.module').then((m) => m.AuthModule),
      },
      {
        path: 'truyen/:slug',
        resolve: {
          novelRes: novelResolver,
          chaptersRes: listChapterResolver,
        },
        loadChildren: () =>
          import('./pages/novel/novel.module').then((m) => m.NovelModule),
      },
      {
        path: 'tim-kiem',
        loadChildren: () =>
          import('./pages/advanced-search/advanced-search.module').then(
            (m) => m.AdvancedSearchModule
          ),
        
      },

      {
        path: 'tu-sach',
        loadChildren: () =>
          import('./pages/bookcase/bookcase.module').then(
            (m) => m.BookcaseModule
          ),
      },
      {
        path: 'the-loai/:slug',
        loadChildren: () =>
          import('./pages/advanced-search/advanced-search.module').then(
            (m) => m.AdvancedSearchModule
          ),
        
      },
      {
        path: 'tai-khoan',
        loadChildren: () =>
          import('./pages/user/user.module').then((m) => m.UserModule),
      },
      {
        path: 'price',
        loadChildren: () =>
          import('./pages/price/price.module').then((m) => m.PriceModule),
      },
      {
        path: 'ban-quyen',
        loadChildren: () =>
          import('./pages/copyright/copyright.module').then(
            (m) => m.CopyrightModule
          ),
      },
      {
        path: 've-chung-toi',
        loadChildren: () =>
          import('./pages/about-us/about-us.module').then((m) => m.AboutUsModule),
      },
      {
        path: 'dieu-khoan',
        loadChildren: () =>
          import('./pages/terms/terms.module').then((m) => m.TermsModule),
      },
      {
        path: ':novel-slug/:chapter-slug/:chapter-id',
        resolve: {
          chapterRes: chapterResolver,
        },
        loadChildren: () =>
          import('./pages/chapter/chapter.module').then((m) => m.ChapterModule),
      },
      {
        path: '**',
        loadChildren: () =>
          import('./pages/not-found/not-found.module').then(
            (m) => m.NotFoundModule
          ),
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    // SEO optimizations
    scrollPositionRestoration: 'top',
    anchorScrolling: 'enabled',
    scrollOffset: [0, 64], // Offset for fixed header
    // Performance optimizations
    preloadingStrategy: PreloadAllModules,
    enableTracing: false, // Set to true only for debugging
    // URL strategy
    urlUpdateStrategy: 'eager'
  })],
  exports: [RouterModule],
})
export class AppRoutingModule { }
