import { isPlatformServer } from '@angular/common';
import {
    ChangeDetectorRef,
    Component,
    ElementRef,
    Inject,
    OnInit,
    PLATFORM_ID
} from '@angular/core';
import { User } from '@schemas/User';
import { AccountService } from '@services/account.service';
import { ImageService } from '@services/image.service';
import { SeoService } from '@services/seo.service';
import { ToastService, ToastType } from '@services/toast.service';
import globalConfig from 'globalConfig';
import { first } from 'rxjs';

@Component({
  selector: 'app-account-component',
  templateUrl: './account.component.html',
  styleUrls: ['./account.component.scss'],
  standalone: false
})
export class UserPageComponent implements OnInit {
  user!: User;
  activeTab: 'profile' | 'bookcase' | 'settings' = 'profile';
  isLoading = true;
  
  constructor(
    private accountService: AccountService,
    private toast: ToastService,
    private elementRef: ElementRef,
    private imageService: ImageService,
    private seoService: SeoService, 
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Object,
  ) { 
    this.seoService.setTitle(`Thông tin tài khoản`);
  }

  ngOnInit(): void {
    if (isPlatformServer(this.platformId)) return;

    this.loadUserData();
  }

  loadUserData() {
    this.isLoading = true;
    this.accountService.GetUserInfo().subscribe((res: any) => {
      if (res.status) {
        this.user = res.data;
      } else {
        console.error(res.message);
        this.toast.show(ToastType.Error, 'Không thể tải thông tin người dùng.');
      }
      this.isLoading = false;
      this.cd.detectChanges();
    });
  }

  switchTab(tab: 'profile' | 'bookcase' | 'settings') {
    this.activeTab = tab;
  }

  // Legacy methods for backward compatibility
  transform(dateString: string | undefined, FORMAT_DATE = 'DD/MM/YYYY'): string {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return '';
    }

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }

  onFileChange(event: any) {
    if (event.target.files && event.target.files.length) {
      const reader = new FileReader();
      if (event.target.files && event.target.files[0]) {
        const file = event.target.files[0];

        const avatar: FormData = new FormData();
        avatar.append('image', file, file.name);
        this.accountService
          .UpdateAvatar(avatar)
          .pipe(first())
          .subscribe((res: any) => {
            if (res.status == 200) {
              this.user.avatar = res.data;

              this.user.token = this.accountService.getAuthorizationToken();
              this.accountService.SaveUser(this.user);

              this.imageService.updateImageUrl(this.user.avatar || '');
              this.toast.show(ToastType.Success, res.message);
            } else {
              this.toast.show(ToastType.Error, res.message);
            }
          });
      }
    }
  }
}
