<div class="common-container">
  <app-breadcrumb
    class="flex py-2"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: 'Tìm kiếm', url: '/tim-kiem' }
    ]"
  >
  </app-breadcrumb>
  <div class="flex flex-col md:flex-row gap-4">
    <div class="sm:basis-64">
      <app-filter
        [selectedValues]="selectedValues"
        (onSubmitSearch)="executeSearch($event)"
      ></app-filter>
    </div>
    <div id="novels" class="relative flex-1">
      <div
        class="text-center absolute top-1/2 left-1/2"
        *ngIf="isLoading; else loadedContent"
      >
        <app-spinner [sizeSpinner]="'40'"></app-spinner>
      </div>
      <ng-template #loadedContent>
        <div class="px-2 overflow-hidden">
          <div class="flex-between mb-4">
            <h2 class="flex-start text-lg font-bold">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                version="1.1"
                width="24"
                height="24"
                viewBox="0 0 256 256"
                enable-background="new 0 0 256 256"
                xml:space="preserve"
                class="h-8 w-8 text-black-500"
                stroke-width="2"
                stroke="currentColor"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <metadata>
                  Svg Vector Icons : http://www.onlinewebfonts.com/icon
                </metadata>
                <g>
                  <g>
                    <path
                      fill="currentColor"
                      d="M14.4,58.3L14.4,58.3h96.3h0.6v0c2.1,0.3,3.7,2.2,3.7,4.4c0,2.3-1.6,4.1-3.7,4.4v0H111l-0.4,0l-0.4,0H14.7l-0.4,0c-2.4,0-4.3-2-4.3-4.5C10,60.3,12,58.3,14.4,58.3z M14.4,106.9L14.4,106.9h96.3h0.6v0c2.1,0.3,3.7,2.2,3.7,4.4c0,2.3-1.6,4.1-3.7,4.4v0H111l-0.4,0l-0.4,0H14.7l-0.4,0c-2.4,0-4.3-2-4.3-4.5C10,108.9,12,106.9,14.4,106.9z M14.4,155.6L14.4,155.6l153.7,0h3.4h0.6v0c2.1,0.3,3.7,2.2,3.7,4.4c0,2.2-1.6,4.1-3.7,4.4v0h-0.3l-0.4,0l-0.4,0h-2.8l-153.6,0l-0.4,0c-2.4,0-4.3-2-4.3-4.5C10,157.6,12,155.6,14.4,155.6z M14.4,204.2L14.4,204.2l153.7,0h3.4h0.6v0c2.1,0.3,3.7,2.2,3.7,4.4s-1.6,4.1-3.7,4.4v0h-0.3l-0.4,0l-0.4,0h-2.8l-153.6,0l-0.4,0c-2.4,0-4.3-2-4.3-4.5C10,206.2,12,204.2,14.4,204.2z M176.2,42.9c14,0,26.8,5.7,35.9,14.9C221.3,67,227,79.7,227,93.7c0,14-5.7,26.8-14.9,35.9c-1.8,1.8-3.7,3.5-5.8,5l39,67.5c1.4,2.4,0.6,5.4-1.8,6.7c-2.4,1.4-5.4,0.6-6.7-1.8l-38.9-67.4c-6.6,3.1-13.9,4.8-21.7,4.8c-14,0-26.8-5.7-35.9-14.9c-9.2-9.2-14.9-21.9-14.9-35.9c0-14,5.7-26.8,14.9-35.9C149.4,48.6,162.1,42.9,176.2,42.9L176.2,42.9z M205.1,64.8c-7.4-7.4-17.7-12-28.9-12c-11.3,0-21.5,4.6-28.9,12c-7.4,7.4-12,17.6-12,28.9c0,11.3,4.6,21.5,12,28.9c7.4,7.4,17.6,12,28.9,12c11.3,0,21.5-4.6,28.9-12c7.4-7.4,12-17.6,12-28.9C217.1,82.4,212.5,72.2,205.1,64.8z"
                    />
                  </g>
                </g>
              </svg>
              <span class="mx-2">({{ this.totalpage * this.step }})</span>KẾT
              QUẢ
            </h2>
            <div
              class="relative flex ml-auto bg-gray-200 dark:bg-neutral-700 rounded-sm overflow-hidden"
            >
              <div
                class="absolute transition-transform duration-100 size-[36px] bg-black cursor-pointer -z-5 pointer-events-none"
                [ngClass]="{ 'translate-x-full': girdType === 0 }"
              ></div>
              <div
                class="p-1.5 cursor-pointer z-10 transition-colors"
                [ngClass]="{
                        active: girdType === 1,
                        'text-white': girdType === 1,
                      }"
                (click)="ChangeGridType($event.target, 1)"
              >
                <svg
                  class="size-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <path
                      d="M3 6H10M3 10H10M3 14H10M3 18H10M14 6L21 6.0006M14 10L21 10.0006M14 14L21 14.0006M14 18L21 18.0006"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                  </g>
                </svg>
              </div>
              <div
                class="p-1.5 cursor-pointer z-10 transition-colors"
                [ngClass]="{
                        active: girdType === 0,
                        'text-white': girdType === 0,
                      }"
                (click)="ChangeGridType($event.target, 0)"
              >
                <svg
                  class="size-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <path
                      d="M14 5.6C14 5.03995 14 4.75992 14.109 4.54601C14.2049 4.35785 14.3578 4.20487 14.546 4.10899C14.7599 4 15.0399 4 15.6 4H18.4C18.9601 4 19.2401 4 19.454 4.10899C19.6422 4.20487 19.7951 4.35785 19.891 4.54601C20 4.75992 20 5.03995 20 5.6V8.4C20 8.96005 20 9.24008 19.891 9.45399C19.7951 9.64215 19.6422 9.79513 19.454 9.89101C19.2401 10 18.9601 10 18.4 10H15.6C15.0399 10 14.7599 10 14.546 9.89101C14.3578 9.79513 14.2049 9.64215 14.109 9.45399C14 9.24008 14 8.96005 14 8.4V5.6Z"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                    <path
                      d="M4 5.6C4 5.03995 4 4.75992 4.10899 4.54601C4.20487 4.35785 4.35785 4.20487 4.54601 4.10899C4.75992 4 5.03995 4 5.6 4H8.4C8.96005 4 9.24008 4 9.45399 4.10899C9.64215 4.20487 9.79513 4.35785 9.89101 4.54601C10 4.75992 10 5.03995 10 5.6V8.4C10 8.96005 10 9.24008 9.89101 9.45399C9.79513 9.64215 9.64215 9.79513 9.45399 9.89101C9.24008 10 8.96005 10 8.4 10H5.6C5.03995 10 4.75992 10 4.54601 9.89101C4.35785 9.79513 4.20487 9.64215 4.10899 9.45399C4 9.24008 4 8.96005 4 8.4V5.6Z"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                    <path
                      d="M4 15.6C4 15.0399 4 14.7599 4.10899 14.546C4.20487 14.3578 4.35785 14.2049 4.54601 14.109C4.75992 14 5.03995 14 5.6 14H8.4C8.96005 14 9.24008 14 9.45399 14.109C9.64215 14.2049 9.79513 14.3578 9.89101 14.546C10 14.7599 10 15.0399 10 15.6V18.4C10 18.9601 10 19.2401 9.89101 19.454C9.79513 19.6422 9.64215 19.7951 9.45399 19.891C9.24008 20 8.96005 20 8.4 20H5.6C5.03995 20 4.75992 20 4.54601 19.891C4.35785 19.7951 4.20487 19.6422 4.10899 19.454C4 19.2401 4 18.9601 4 18.4V15.6Z"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                    <path
                      d="M14 15.6C14 15.0399 14 14.7599 14.109 14.546C14.2049 14.3578 14.3578 14.2049 14.546 14.109C14.7599 14 15.0399 14 15.6 14H18.4C18.9601 14 19.2401 14 19.454 14.109C19.6422 14.2049 19.7951 14.3578 19.891 14.546C20 14.7599 20 15.0399 20 15.6V18.4C20 18.9601 20 19.2401 19.891 19.454C19.7951 19.6422 19.6422 19.7951 19.454 19.891C19.2401 20 18.9601 20 18.4 20H15.6C15.0399 20 14.7599 20 14.546 19.891C14.3578 19.7951 14.2049 19.6422 14.109 19.454C14 19.2401 14 18.9601 14 18.4V15.6Z"
                      stroke="currentColor"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></path>
                  </g>
                </svg>
              </div>
            </div>
          </div>
          <div *ngIf="length > 0; else empty">
            <div
              class="grid {{
                girdType == 0
                  ? 'grid-cols-3 xl:grid-cols-5 lg:grid-cols-4'
                  : 'grid-cols-1 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-2'
              }} gap-2 mt-1"
            >
              <ng-container *ngFor="let novel of completeNovels">
                <app-card-v1 *ngIf="girdType == 0" [novel]="novel">
                </app-card-v1>
                <app-card-v2 *ngIf="girdType == 1" [novel]="novel">
                </app-card-v2>
              </ng-container>
            </div>
            <app-pagination
              [currentPage]="currentPage"
              [totalpage]="totalpage"
              [rootLink]="'/tim-kiem'"
            >
            </app-pagination>
          </div>

          <ng-template #empty>
            <div
              class="w-1/3 mt-20 lg:min-h-1/2 lg:w-42 flex-center mx-auto"
            >
              <app-empty [message]="'Không tìm thấy truyện'"></app-empty>
            </div>
          </ng-template>
        </div>
      </ng-template>
    </div>
  </div>
</div>
