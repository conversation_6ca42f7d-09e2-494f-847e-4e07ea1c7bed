.genre-page {
  @apply min-h-screen bg-gray-50 dark:bg-dark-background;
}

// Breadcrumb Navigation
.breadcrumb-nav {
  @apply bg-white dark:bg-dark-700 border-b border-gray-200 dark:border-dark-600 py-3;
}

.breadcrumb-list {
  @apply flex items-center max-w-6xl mx-auto px-4 text-sm;
}

.breadcrumb-item {
  @apply flex items-center;
}

.breadcrumb-link {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200;
}

.breadcrumb-current {
  @apply text-gray-700 dark:text-gray-300 font-medium;
}

.breadcrumb-separator {
  @apply mx-2 text-gray-400 dark:text-gray-500;
}

// Genre Header
.genre-header {
  @apply bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white py-12 lg:py-16;
}

.header-content {
  @apply max-w-6xl mx-auto px-4 text-center;
}

.genre-title {
  @apply text-2xl lg:text-4xl font-bold mb-4 leading-tight;
}

.genre-description {
  @apply text-lg lg:text-xl mb-6 max-w-4xl mx-auto leading-relaxed opacity-90;
}

.genre-stats {
  @apply flex justify-center gap-6 flex-wrap;
}

.stat-item {
  @apply text-center;
}

.stat-number {
  @apply block text-xl lg:text-2xl font-bold text-yellow-300;
}

.stat-label {
  @apply block text-sm opacity-80;
}

// Filter Section
.filter-section {
  @apply bg-white dark:bg-dark-700 border-b border-gray-200 dark:border-dark-600 py-4;
}

.filter-container {
  @apply max-w-6xl mx-auto px-4 flex flex-wrap items-center gap-4 justify-between;
}

.filter-group {
  @apply flex items-center gap-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.filter-select {
  @apply bg-white dark:bg-dark-600 border border-gray-300 dark:border-dark-500 rounded-md px-3 py-2 text-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply text-gray-900 dark:text-white;
}

.results-info {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

// Loading State
.loading-container {
  @apply flex flex-col items-center justify-center py-16;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-4;
}

.loading-text {
  @apply text-gray-600 dark:text-gray-400;
}

// Novels Section
.novels-section {
  @apply max-w-6xl mx-auto px-4 py-8;
}

.novels-grid {
  @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 mb-8;
}

.novel-item {
  @apply transition-transform duration-200 hover:scale-105;
}

// Pagination
.pagination-nav {
  @apply mt-8;
}

.pagination-container {
  @apply flex items-center justify-center gap-2;
}

.pagination-btn {
  @apply px-4 py-2 bg-white dark:bg-dark-600 border border-gray-300 dark:border-dark-500 rounded-md;
  @apply text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-500;
  @apply transition-colors duration-200 font-medium;

  &.disabled {
    @apply opacity-50 cursor-not-allowed hover:bg-white dark:hover:bg-dark-600;
  }
}

.page-numbers {
  @apply flex items-center gap-1;
}

.page-btn {
  @apply w-10 h-10 flex items-center justify-center rounded-md border border-gray-300 dark:border-dark-500;
  @apply bg-white dark:bg-dark-600 text-gray-700 dark:text-gray-300;
  @apply hover:bg-gray-50 dark:hover:bg-dark-500 transition-colors duration-200;

  &.active {
    @apply bg-blue-600 border-blue-600 text-white hover:bg-blue-700;
  }
}

.page-ellipsis {
  @apply px-2 text-gray-500 dark:text-gray-400;
}

// Empty State
.empty-state {
  @apply flex items-center justify-center py-16;
}

.empty-content {
  @apply text-center max-w-md;
}

.empty-icon {
  @apply w-16 h-16 mx-auto mb-4 text-gray-400 dark:text-gray-500;
}

.empty-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-2;
}

.empty-description {
  @apply text-gray-600 dark:text-gray-400 mb-6;
}

.empty-action {
  @apply inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg;
  @apply transition-colors duration-200;
}

// FAQ Section
.genre-faq {
  @apply py-12 bg-white dark:bg-dark-800;
}

// Responsive Design
@media (max-width: 640px) {
  .genre-title {
    @apply text-xl;
  }

  .genre-description {
    @apply text-base;
  }

  .genre-stats {
    @apply gap-4;
  }

  .stat-number {
    @apply text-lg;
  }

  .filter-container {
    @apply flex-col items-start gap-3;
  }

  .novels-grid {
    @apply grid-cols-2 gap-3;
  }

  .pagination-container {
    @apply flex-wrap;
  }

  .pagination-btn {
    @apply px-3 py-1 text-sm;
  }

  .page-btn {
    @apply w-8 h-8 text-sm;
  }
}