import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpResponse } from "@angular/common/http";
import { Injectable, makeStateKey, StateKey, TransferState } from "@angular/core";
import { of, tap } from "rxjs";

interface TransferHttpResponse {
    body: any;
    headers: Record<string, string[]>;
    status?: number;
    statusText?: string;
    url?: string;
    responseType?: HttpRequest<unknown>['responseType'];
    expires: number;
}
@Injectable()
export class CacheInterceptor implements HttpInterceptor {

    constructor(private transferState: TransferState) { }

    intercept(req: HttpRequest<any>, next: HttpHandler) {

        if (!req.transferCache) {
            return next.handle(req);
        }

        const storeKey = this.makeCacheKey(req);
        const response = this.transferState.get(storeKey, null);
        if (response && response.expires > Date.now()) {
            // Request found in cache. Respond using it.
            let body: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> | Blob | string | undefined = response.body;

            switch (response.responseType) {
                case 'arraybuffer':
                    body = new TextEncoder().encode(response.body).buffer;
                    break;
                case 'blob':
                    body = new Blob([response.body]);
                    break;
            }

            return of(
                new HttpResponse({
                    body,
                    headers: new HttpHeaders(response.headers),
                    status: response.status,
                    statusText: response.statusText,
                    url: response.url,
                }),
            );
        }
        const expiration = Number(req.params.get('expiration') ?? 60);

        // Request not found in cache. Make the request and cache it.
        return next.handle(req).pipe(
            tap((event: HttpEvent<unknown>) => {
                if (event instanceof HttpResponse) {
                    this.transferState.set<TransferHttpResponse>(storeKey, {
                        body: event.body,
                        headers: this.getHeadersMap(event.headers),
                        status: event.status,
                        statusText: event.statusText,
                        url: event.url || '',
                        responseType: req.responseType,
                        expires: Date.now() + 1000 * expiration,
                    });
                }
            }),
        );
        // return next.handle(req);

    }
    generateHash(value: string): string {
        let hash = 0;
        for (const char of value) {
            hash = Math.imul(31, hash) + char.charCodeAt(0) << 0;
        }
        hash += 2147483647 + 1;
        return hash.toString();
    }

    makeCacheKey(request: HttpRequest<any>): StateKey<TransferHttpResponse> {
        // make the params encoded same as a url so it's easy to identify
        const { method, responseType, url } = request;
        const _URL = new URL(url);
        const key = method + '.' + responseType + '.' + _URL.pathname + _URL.search;
        const hash = this.generateHash(key);
        return makeStateKey(hash);
    }
    getHeadersMap(headers: HttpHeaders): Record<string, string[]> {
        const headersMap: Record<string, string[]> = {};

        for (const key of headers.keys()) {
            const values = headers.getAll(key);
            if (values !== null) {
                headersMap[key] = values;
            }
        }

        return headersMap;
    }
}