<app-panel class="w-full flex h-full" [title]="title">
  <div class="mx-2 mt-2 flex flex-col h-full w-full" *ngIf="hasValidNovels">
    <div class="flex mb-4">
      <a
        [routerLink]="['/truyen', novels[0].url + '-' + novels[0].id]"
        class="w-14 h-20 relative flex-shrink-0"
        [ngStyle]="{ perspective: '300px',}"
      >
        <div
          class="absolute top-0 right-0 left-0 bottom-0"
          [ngStyle]="{
            transform: 'rotateY(30deg)',
            transformOrigin: 'right',
            transformStyle: 'preserve-3d'
          }"
        >
          <span
            class="left-[0.5px] top-[1.5%] absolute h-[98.5%] w-4 bg-neutral-200 shadow-neutral-500 shadow-[inset_0_0_5px_2px] -z-10 transition-transform duration-500"
            [style]="{
              transformOrigin: 'left',
              transform: 'rotateY(90deg)'
            }"
          ></span>
          <img
            class="object-cover size-full"
            [src]="novels[0].coverImage"
            onerror="this.src = 'https://static.saytruyenhot.com/coverimg/ban-trai-toi-la-hoc-ba.jpg'"
            [alt]="novels[0].title"
            loading="lazy"
          />
        </div>
      </a>
      <div class="flex flex-row ml-4 items-center">
        <div>
          <a
            [routerLink]="['/truyen', novels[0].url + '-' + novels[0].id]"
            class="font-semibold text-lg sm:text-base line-clamp-2"
          >
            {{ novels[0].title }}
          </a>
          <div class="flex gap-2">
            <app-icon-author class="size-4 text-gray-600 dark:text-white"> </app-icon-author>
            <span class="text-sm lg:text-xs text-gray-600 dark:text-white">
              {{ novels[0].author }}
            </span>
            <!-- <span class="text-sm text-gray-500 dark:text-dark-100 ml-1">
                    ({{ novels[0].chapters.length }} Chương)
                  </span> -->
          </div>
        </div>
        <span class="text-2xl font-bold mr-2 text-red-500">
          <svg
            class="size-10"
            viewBox="0 0 120 120"
            id="Layer_1"
            version="1.1"
            xml:space="preserve"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            fill="#000000"
          >
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g
              id="SVGRepo_tracerCarrier"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></g>
            <g id="SVGRepo_iconCarrier">
              <style type="text/css">
                .st0 {
                  fill: #e24255;
                }

                .st1 {
                  fill: #ffc54d;
                }

                .st2 {
                  fill: #e8b04b;
                }

                .st3 {
                  fill: #ffffff;
                }
              </style>
              <g>
                <polygon
                  class="st0"
                  points="79.7,45.6 60,55.5 40.3,45.6 15.9,94.3 31.1,92.8 38.9,105.9 60,63.9 81.1,105.9 88.9,92.8 104.1,94.3 "
                ></polygon>
                <circle class="st1" cx="60" cy="46.4" r="32.2"></circle>
                <circle class="st2" cx="60" cy="46.4" r="25.3"></circle>
                <path
                  class="st3"
                  d="M61.2,31.2l4.2,8.4c0.2,0.4,0.6,0.7,1,0.8l9.3,1.4c1.1,0.2,1.6,1.5,0.8,2.3l-6.7,6.6c-0.3,0.3-0.5,0.8-0.4,1.2 l1.6,9.3c0.2,1.1-1,2-2,1.4l-8.3-4.4c-0.4-0.2-0.9-0.2-1.3,0L51,62.6c-1,0.5-2.2-0.3-2-1.4l1.6-9.3c0.1-0.4-0.1-0.9-0.4-1.2 l-6.7-6.6c-0.8-0.8-0.4-2.2,0.8-2.3l9.3-1.4c0.4-0.1,0.8-0.3,1-0.8l4.2-8.4C59.3,30.2,60.7,30.2,61.2,31.2z"
                ></path>
              </g>
            </g>
          </svg>
        </span>
      </div>
    </div>
    <div class="flex flex-col">
      <ng-container *ngFor="let i of novelIndices; trackBy: trackByIndex">
        <a
          *ngIf="getNovel(i) as novel"
          [routerLink]="['/truyen', novel.url + '-' + novel.id]"
          class="flex-start h-9 border-t-[1px] border-blue-100 dark:border-dark-500 p-2 gap-1 mt-1"
        >
          <span
            class="text-lg bg-clip-text text-transparent bg-primary-100 font-extrabold mr-2 text-center rounded-full italic bg-gradient-to-r from-primary-300 via-primary-200 to-primary-50"
          >
            {{ i + 1 }}
          </span>
          <span class="flex w-full justify-between gap-1.5">
            <p class="font-light line-clamp-1">
              {{ novel.title }}
            </p>

            <p class="text-sm text-gray-500 dark:text-dark-100 ml-auto">
              {{ novel.viewCount | numeral }}
            </p>
          </span>
        </a>
      </ng-container>
    </div>
  </div>
</app-panel>
