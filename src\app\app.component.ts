import { isPlatformServer, ViewportScroller } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject, OnInit, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { NavigationEnd, Router, Scroll } from '@angular/router';
import { SeoService } from '@services/seo.service';
import { filter } from 'rxjs';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  standalone: false,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppComponent implements OnInit {
  title = 'novels-app';
  
  ngOnInit() {
    this.setupBaseSeo();
  }

  constructor(
    private router: Router,
    private viewportScroller: ViewportScroller,
    private seoService: SeoService,
    @Inject(PLATFORM_ID) private platformId: Object,
  ) {
    if (isPlatformServer(this.platformId)) return;

    this.setupScrollBehavior();
    // this.trackPageViews();
  }

  private setupBaseSeo() {
    this.seoService.addCommonSchema();
  }

  private setupScrollBehavior() {
    this.router.events
      .pipe(filter((e) => e instanceof Scroll))
      .subscribe((e: any) => {
        if (e.position) {
          // backward navigation
          window.scrollTo({
            top: e.position[1],
            left: e.position[0],
            behavior: 'instant',
          });
        } else if (e.anchor) {
          this.viewportScroller.scrollToAnchor(e.anchor);
        } else {
          window.scrollTo({ top: 0, left: 0, behavior: 'instant' });
        }
      });
  }

  private trackPageViews() {
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        // Track page views for analytics if needed
        if (typeof (window as any).gtag !== 'undefined') {
          (window as any).gtag('config', 'GA_MEASUREMENT_ID', {
            page_path: event.urlAfterRedirects
          });
        }
      });
  }
}
