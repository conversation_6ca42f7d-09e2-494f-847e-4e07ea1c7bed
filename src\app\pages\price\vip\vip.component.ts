import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FeatureFlags } from '@schemas/enum';
import { Item } from '@schemas/Item';
import { FeatureFlagsService } from '@services/feature-flags.service';
import { PaymentService } from '@services/payment.service';
import { SeoService } from '@services/seo.service';
import { ToastService, ToastType } from '@services/toast.service';
import globalConfig from 'globalConfig';

@Component({
  selector: 'app-vip',
  imports: [CommonModule],
  templateUrl: './vip.component.html',
  styleUrl: './vip.component.scss'
})
export class VIPComponent {
  items: Item[] = [];
  appName = globalConfig.APP_NAME;
  
  constructor(
    private paymentService: PaymentService,
    private toastService: ToastService,
    private seoService: SeoService,
    private featureFlags: FeatureFlagsService
  ) {
    this.setupSeo();
  }

  ngOnInit() {
    this.paymentService.getBuyItems().subscribe(res => {
      if (res.status === 1) {
        this.items = res.data!.filter(item => item.type === 'VIP');
      }
    });
  }

  buyVip(item: Item) {
    if (!this.featureFlags.isEnabled(FeatureFlags.FEATURE_VIP)) {
      this.toastService.show(ToastType.Success, `Tính năng VIP đang phát triển`);
      return;
    }

    this.paymentService.buyItem(item).subscribe(res => {
      if (res.status === 1) {
        this.toastService.show(ToastType.Success, `Bạn đã mua thành công gói ${item.name}`);
      } else {
        this.toastService.show(ToastType.Error, `Đã có lỗi xảy ra vui lòng kiểm tra lại`);
      }
    });
  }

  private setupSeo(): void {
    const title = `Nâng cấp VIP`;
    const description = `Nâng cấp tài khoản VIP tại ${this.appName} để trải nghiệm đọc truyện chữ premium. Không quảng cáo, tốc độ tải nhanh, nhiều tính năng độc quyền và ưu tiên cập nhật truyện mới.`;
    const url = `${globalConfig.BASE_URL}/price/vip`;

    const seoData = {
      title,
      description,
      type: 'website' as const,
      image: `${globalConfig.BASE_URL}/logo.png`,
      url,
      siteName: this.appName,
      locale: 'vi_VN',
      twitterCard: 'summary' as const,
      keywords: `nâng cấp VIP ${this.appName}, tài khoản VIP, đọc truyện premium, không quảng cáo, VIP membership, gói VIP đọc truyện`,
      canonical: url,
      section: 'Dịch vụ VIP',
      noindex: false,
      nofollow: false
    };

    this.seoService.setSEOData(seoData);

    // Add structured data schemas
    const schemas = [
      this.generateVIPServiceSchema(),
      this.seoService.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: '/' },
        { name: 'Bảng giá', url: '/price' },
        { name: 'Nâng cấp VIP', url: '/price/vip' }
      ])
    ];

    this.seoService.addStructuredData(schemas);
  }

  private generateVIPServiceSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'Service',
      'name': `Dịch vụ VIP ${this.appName}`,
      'description': `Nâng cấp tài khoản VIP để trải nghiệm đọc truyện premium tại ${this.appName}`,
      'url': `${globalConfig.BASE_URL}/price/vip`,
      'provider': {
        '@type': 'Organization',
        'name': this.appName,
        'url': globalConfig.BASE_URL
      },
      'serviceType': 'VIP Membership',
      'areaServed': 'VN',
      'category': 'Entertainment',
      'hasOfferCatalog': {
        '@type': 'OfferCatalog',
        'name': 'Gói VIP',
        'itemListElement': [
          {
            '@type': 'Offer',
            'name': 'VIP Premium',
            'description': 'Trải nghiệm đọc truyện không quảng cáo với nhiều tính năng độc quyền'
          }
        ]
      },
      'additionalProperty': [
        {
          '@type': 'PropertyValue',
          'name': 'Không quảng cáo',
          'value': 'true'
        },
        {
          '@type': 'PropertyValue',
          'name': 'Tốc độ tải nhanh',
          'value': 'true'
        },
        {
          '@type': 'PropertyValue',
          'name': 'Ưu tiên cập nhật',
          'value': 'true'
        }
      ],
      'breadcrumb': {
        '@type': 'BreadcrumbList',
        'itemListElement': [
          {
            '@type': 'ListItem',
            'position': 1,
            'name': 'Trang chủ',
            'item': globalConfig.BASE_URL
          },
          {
            '@type': 'ListItem',
            'position': 2,
            'name': 'Bảng giá',
            'item': `${globalConfig.BASE_URL}/price`
          },
          {
            '@type': 'ListItem',
            'position': 3,
            'name': 'Nâng cấp VIP',
            'item': `${globalConfig.BASE_URL}/price/vip`
          }
        ]
      }
    };
  }
}
