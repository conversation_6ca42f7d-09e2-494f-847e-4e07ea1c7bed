import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, Output } from '@angular/core';

@Component({
  standalone: true,
  imports: [CommonModule],
  selector: 'app-color-select',
  templateUrl: './color-select.component.html',
  styleUrl: './color-select.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,

})
export class ColorSelectComponent {

  @Input()
  name: string = 'Chọn màu'; // Nhãn hiển thị cho màu sắc
  @Input()
  value: string = '#FF0000'; // Mặc định là màu đỏ

  @Output()
  valueChange = new EventEmitter<string>(); // Sự kiện phát ra khi giá trị thay đổi

  @Input()
  options: { label: string; value: string }[] = []
  constructor(public el: ElementRef) {
  }
  onColorChange(colorValue: string) {
    this.value = colorValue; // Cập nhật giá trị màu khi người dùng chọn màu mới
    this.valueChange.emit(this.value); // Phát ra sự kiện valueChange với giá trị mới
    
  }
}
