import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { BreadcrumbComponent } from '@components/breadcrumb/breadcrumb.component';
import { ChapterPopupComponent } from '@components/chapter-popup/chapter-popup.component';
import { NovelCardV1Component } from '@components/novel-card/card-v1/card-v1.component';
import { PopupComponent } from '@components/popup/popup.component';
import { SelectComponent } from '@components/select/select.component';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { TutorialDirective } from '@directives/tutorial.directive';
import { PinesModule } from 'src/app/shared/pines/pines.module';
import { ChapterComponent } from './chapter.component';
import { SpinnerComponent } from '@components/spinner/spinner.component';

const routes: Routes = [{ path: '', component: ChapterComponent }];

@NgModule({
    declarations: [ChapterComponent],
    imports: [
        FormsModule,
        CommonModule,
        PopupComponent,
        BreadcrumbComponent,
        ChapterPopupComponent,
        PinesModule,
        NovelCardV1Component,
        ClickOutsideDirective,
        SelectComponent,
        TutorialDirective,
        SpinnerComponent,
        RouterModule.forChild(routes)
    ]
})
export class ChapterModule { }
