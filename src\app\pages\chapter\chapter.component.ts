import { isPlatformServer } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ComponentRef,
  ElementRef,
  EventEmitter,
  HostListener,
  Inject,
  PLATFORM_ID,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AppSettingComponent } from '@components/app-setting/app-setting.component';
import { Chapter } from '@schemas/Chapter';
import { ChapterPage } from '@schemas/ChapterPage';
import { Novel } from '@schemas/Novel';
import { IServiceResponse } from '@schemas/ResponseType';
import { SettingType } from '@schemas/SettingType.enum';
import { DynamicLoadingService } from '@services/dynamic.loading.service';
import { HistoryService } from '@services/history.service';
import { NovelService } from '@services/novel.service';
import { SeoService } from '@services/seo.service';
import { SettingService } from '@services/setting.service';
import globalConfig from 'globalConfig';
import { AudioComponent } from './audio/audio.component';

@Component({
  selector: 'app-chapter',
  standalone: false,
  templateUrl: './chapter.component.html',
  styleUrl: './chapter.component.scss',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,

})
export class ChapterComponent {

  similarNovels: Novel[] = [];
  scrollSpeed = 3;
  chapters: Chapter[] = [];
  offsetTop = 0;
  chapter?: ChapterPage;
  fixedHeader = false;
  fixedToolbar = false;
  globalFixedToolbar = true;
  texture = '';
  toolbarState =
    {
      isShowPlayer: false,
      isShowSetting: false,
      isOpenChapterPopup: false,
      isFullScreen: false,
      isScrollDown: false,
    }

  @ViewChild('content') contentRef: ElementRef<HTMLElement> | null = null;
  @ViewChild('toolbar') toolbarRef: ElementRef<HTMLElement> | null = null;
  @ViewChild('chapterContainer') chapterContainerRef: ElementRef<HTMLElement> | null = null;

  audioComponent?: ComponentRef<AudioComponent>;
  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private novelService: NovelService,
    private settingService: SettingService,
    private route: ActivatedRoute,
    private dynamicLoadingService: DynamicLoadingService,
    private seoService: SeoService,
    private router: Router,
    private cd: ChangeDetectorRef,
    private historyService: HistoryService,
  ) { }

  isServer(): boolean {
    return isPlatformServer(this.platformId);
  }

  showPlayer() {
    this.toolbarState.isShowPlayer = !this.toolbarState.isShowPlayer
    this.audioComponent = this.dynamicLoadingService.createDynamicComponent<AudioComponent>(AudioComponent)
    if (this.audioComponent) {
      this.audioComponent.instance.setChapterPage(this.chapter!);

      this.audioComponent.instance.setVisible(this.toolbarState.isShowPlayer);
      this.audioComponent.instance.isVisibleChange = new EventEmitter<boolean>();
      this.audioComponent.instance.isVisibleChange.subscribe((visible) => {
        this.toolbarState.isShowPlayer = visible;
      })
    }
  }


  ngOnInit() {
    this.route.data.subscribe((params) => {
      let chapterRes = params["chapterRes"] as IServiceResponse<ChapterPage>;
      this.chapter = chapterRes?.data;
      if (!chapterRes || chapterRes.status == 0 || !this.chapter) {
        this.router.navigate(['/not-found']);
        return;
      }
      if (this.audioComponent) {
        this.audioComponent.instance.setChapterPage(this.chapter!);

      }
      this.novelService
        .getChapterNeighbors(this.chapter.id)
        .subscribe((res: IServiceResponse<Chapter[]>) => {
          if (!this.chapter) return;
          if (!res.data || res.data.length == 0) return;
          if (res.data[0].slug < this.chapter!.slug) {
            this.chapter.prevChapter = res.data[0];
            this.chapter.nextChapter = res.data.length > 1 ? res.data[1] : undefined;
          } else {
            this.chapter.prevChapter = res.data.length > 1 ? res.data[1] : undefined;
            this.chapter.nextChapter = res.data[0];
          }

          this.cd.markForCheck();
        });

      this.SetupSeo(this.chapter.novel, this.chapter);
      this.novelService
        .getChapterContent(this.chapter!.id)
        .subscribe((res: any) => {
          this.chapter!.content = res.content.join('<br><br>');
          this.cd.markForCheck();
        });

      if (!this.isServer()) {
        this.historyService.SaveHistory(this.chapter.novel, this.chapter);
        this.novelService
          .getSimilarNovel(this.chapter.novel.id)
          .subscribe((res: IServiceResponse<Novel[]>) => {
            if (res.data) this.similarNovels = res.data;
            this.cd.markForCheck();
          });

      }
      this.cd.markForCheck();
    });



    this.registerEventInit();
  }

  ngOnDestroy() {
    if (!this.isServer()) {
      speechSynthesis.cancel();
    }
  }
  ngAfterViewInit() {
    if (!this.isServer()) {

      const rect = this.toolbarRef!.nativeElement.getBoundingClientRect();
      this.offsetTop = rect.y;
      this.registerEventAfter();
    }
  }

  registerEventInit() {

    this.settingService.OnSettingChange(SettingType.FixedToolbar).subscribe((setting) => {
      this.globalFixedToolbar = setting.value;
      if (!this.globalFixedToolbar)
        this.fixedToolbar = false
    })
    this.settingService.OnSettingChange(SettingType.FixedHeader).subscribe((setting) => {
      this.fixedHeader = setting.value;
    })
    this.settingService.OnSettingChange(SettingType.Texture).subscribe((setting) => {
      this.texture = setting.value
    })
  }

  registerEventAfter() {
    this.settingService.OnSettingChange(SettingType.FontSize).subscribe((setting) => {
      this.contentRef!.nativeElement!.style.fontSize = setting.value + 'px';
    });

    this.settingService.OnSettingChange(SettingType.FontFamily).subscribe((setting) => {
      this.contentRef!.nativeElement!.style.fontFamily = setting.value;
    })

    this.settingService.OnSettingChange(SettingType.LineHeight).subscribe((setting) => {
      this.contentRef!.nativeElement!.style.lineHeight = setting.value + '';
    })

  }

  @HostListener('window:click', [])
  onPageClick() {
    // this.scrollState = false;
  }


  @HostListener('window:scroll', [])
  onWindowScroll() {
    if (!this.globalFixedToolbar) return
    if (window.scrollY > this.chapterContainerRef!.nativeElement.offsetTop - 64 && !this.fixedToolbar) {
      this.fixedToolbar = true;

    }
    if (window.scrollY <= this.chapterContainerRef!.nativeElement.offsetTop - 64 && this.fixedToolbar) {
      {
        this.fixedToolbar = false;
      }
    }



  }
  scrollSlowDown() {
    if (this.toolbarState.isScrollDown) {
      this.toolbarState.isScrollDown = false;
      return;
    }
    let startTime = performance.now();
    let d = 0;
    this.toolbarState.isScrollDown = true;
    let scrollStep = (timestamp: number) => {
      const elapsedTime = timestamp - startTime;
      startTime = timestamp;
      d += (elapsedTime * this.scrollSpeed) / 100;
      if (d >= 0.5) {
        window.scroll({
          top: window.scrollY + d,
          left: 0,
          behavior: 'smooth',
        });
        d = 0;
      }
      if (this.toolbarState.isScrollDown && this.contentRef!.nativeElement.offsetTop + this.contentRef!.nativeElement.clientHeight - window.scrollY > window.innerHeight) {
        requestAnimationFrame(scrollStep); // Tiếp tục cuộn nếu chưa hết 10 giây
      }
      else {
        this.toolbarState.isScrollDown = false
      }
    };

    requestAnimationFrame(scrollStep); // Bắt đầu cuộn
  }
  @HostListener('document:fullscreenchange', ['$event'])
  onFullscreenChange(event: Event) {
    this.toolbarState.isFullScreen = document.fullscreenElement != null
  }

  enterFullscreen(): void {
    let elem: any = this.chapterContainerRef!.nativeElement;
    if (document.fullscreenElement) {
      document.exitFullscreen();

      return;
    }
    if (elem.requestFullscreen) {
      elem.requestFullscreen();
    }
    else if (elem.mozRequestFullScreen) {
      // Firefox
      elem.mozRequestFullScreen();
    } else if (elem.webkitRequestFullscreen) {
      // Chrome, Safari, and Opera
      elem.webkitRequestFullscreen();
    } else if (elem.msRequestFullscreen) {
      // IE/Edge
      elem.msRequestFullscreen();
    }
  }

  onOpenSetting() {
    let component1 = this.dynamicLoadingService.createDynamicComponent<AppSettingComponent>(AppSettingComponent)
    component1?.instance.open(2);
    // this.toolbarState.isShowSetting = true
  }

  // items: { icon: string, action: () => void }[] = [
  //   {
  //     action: () => this.scrollSlowDown(),
  //     icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="size-5" > <path   d="M12 6C10.8954 6 10 5.10457 10 4C10 2.89543 10.8954 2 12 2C13.1046 2 14 2.89543 14 4C14 5.10457 13.1046 6 12 6Z"   stroke="currentColor"   stroke-width="1.5"   stroke-linecap="round"   stroke-linejoin="round" /> <path   d="M12 9L12 22M12 22L15 19M12 22L9 19"   stroke="currentColor"   stroke-width="1.5"   stroke-linecap="round"   stroke-linejoin="round" /></svg>`,
  //   },
  //   {
  //     action: () => this.enterFullscreen(),
  //     icon: `<svg  fill="none" viewBox="0 0 24 24" stroke="currentColor" class="size-5 text-write"><path  stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path></svg>`,
  //   },

  // ];


  SetupSeo(novel: Novel, chapter: Chapter) {
    const chapterTitle = `Chương ${chapter.slug}`;
    const title = `${novel.title} - ${chapterTitle}`;
    const description = `${chapterTitle}: Đọc truyện "${novel.title}"${novel.author ? ' của ' + novel.author : ''}. Nội dung mới nhất, chất lượng, cập nhật liên tục tại ${globalConfig.APP_NAME}.`;
    const url = `${globalConfig.BASE_URL}/${novel.url}/chuong-${chapter.slug}/${chapter.id}`;

    const seoData = {
      title,
      description,
      type: 'article' as const,
      image: novel.coverImage || `${globalConfig.BASE_URL}/logo.png`,
      url,
      siteName: globalConfig.APP_NAME,
      locale: 'vi_VN',
      twitterCard: 'summary' as const,
      keywords: `${novel.title}, ${chapterTitle}, ${novel.author || 'tác giả ẩn danh'}, đọc truyện, truyện online, ${novel.genres.map(g => g.title).join(', ')}, đọc truyện miễn phí, ${globalConfig.APP_NAME}`,
      author: novel.author || 'Tác giả ẩn danh',
      publishedTime: chapter.updateAt || new Date().toISOString(),
      modifiedTime: chapter.updateAt || new Date().toISOString(),
      section: 'Truyện chữ',
      canonical: url,
      tags: novel.genres.map(g => g.title),
      noindex: false,
      nofollow: false
    };

    this.seoService.setSEOData(seoData);

    // Add structured data schemas
    const schemas = [
      this.seoService.generateChapterSchema(novel, chapter),
      this.seoService.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: '/' },
        { name: novel.title, url: `/truyen/${novel.url}-${novel.id}` },
        { name: chapterTitle, url: `/${novel.url}/chuong-${chapter.slug}/${chapter.id}` }
      ])
    ];

    this.seoService.addStructuredData(schemas);
  }
}
