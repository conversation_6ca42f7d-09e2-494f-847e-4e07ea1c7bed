<div class="settings-container">
  <!-- Header -->
  <div class="settings-header">
    <h2 class="settings-title"><PERSON><PERSON>i đặt tài k<PERSON>n</h2>
    <p class="settings-subtitle"><PERSON><PERSON><PERSON><PERSON> lý thông tin cá nhân và tùy chỉnh trải nghiệm</p>
  </div>

  <div class="settings-layout">
    <!-- Sidebar Navigation -->
    <div class="settings-sidebar">
      <nav class="settings-nav">
        <button 
          class="nav-item"
          [class.active]="activeSection === 'profile'"
          (click)="switchSection('profile')"
        >
          <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          Thông tin cá nhân
        </button>

        <button 
          class="nav-item"
          [class.active]="activeSection === 'privacy'"
          (click)="switchSection('privacy')"
        >
          <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
          Riêng tư
        </button>

        <button 
          class="nav-item"
          [class.active]="activeSection === 'notifications'"
          (click)="switchSection('notifications')"
        >
          <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM11 17H6l5 5v-5zM7 8a5 5 0 1110 0v3l2 2v2H5v-2l2-2V8z"></path>
          </svg>
          Thông báo
        </button>

        <button 
          class="nav-item"
          [class.active]="activeSection === 'preferences'"
          (click)="switchSection('preferences')"
        >
          <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
          Đọc truyện
        </button>
      </nav>
    </div>

    <!-- Main Content -->
    <div class="settings-content">
      <!-- Profile Settings -->
      <div *ngIf="activeSection === 'profile'" class="settings-section">
        <div class="section-header">
          <h3 class="section-title">Thông tin cá nhân</h3>
          <p class="section-subtitle">Cập nhật thông tin cơ bản của tài khoản</p>
        </div>

        <form class="settings-form" (ngSubmit)="updateProfile()">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">Họ</label>
              <input 
                type="text" 
                class="form-input"
                [(ngModel)]="profileForm.firstName"
                name="firstName"
                placeholder="Nhập họ của bạn"
              />
            </div>
            <div class="form-group">
              <label class="form-label">Tên</label>
              <input 
                type="text" 
                class="form-input"
                [(ngModel)]="profileForm.lastName"
                name="lastName"
                placeholder="Nhập tên của bạn"
              />
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">Email</label>
            <input 
              type="email" 
              class="form-input"
              [(ngModel)]="profileForm.email"
              name="email"
              placeholder="<EMAIL>"
            />
          </div>

          <div class="form-group">
            <label class="form-label">Ngày sinh</label>
            <input 
              type="date" 
              class="form-input"
              [(ngModel)]="profileForm.dob"
              name="dob"
            />
          </div>

          <div class="form-group">
            <label class="form-label">Châm ngôn</label>
            <textarea 
              class="form-textarea"
              [(ngModel)]="profileForm.maxim"
              name="maxim"
              placeholder="Chia sẻ châm ngôn của bạn..."
              rows="3"
            ></textarea>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary">Cập nhật thông tin</button>
            <button type="button" class="btn btn-secondary" (click)="changePassword()">Đổi mật khẩu</button>
          </div>
        </form>
      </div>

      <!-- Privacy Settings -->
      <div *ngIf="activeSection === 'privacy'" class="settings-section">
        <div class="section-header">
          <h3 class="section-title">Cài đặt riêng tư</h3>
          <p class="section-subtitle">Kiểm soát ai có thể xem thông tin của bạn</p>
        </div>

        <form class="settings-form" (ngSubmit)="updatePrivacySettings()">
          <div class="form-group">
            <label class="form-label">Hiển thị hồ sơ</label>
            <select class="form-select" [(ngModel)]="privacySettings.profileVisibility" name="profileVisibility">
              <option value="public">Công khai</option>
              <option value="friends">Chỉ bạn bè</option>
              <option value="private">Riêng tư</option>
            </select>
          </div>

          <div class="form-group">
            <label class="toggle-label">
              <input 
                type="checkbox" 
                class="toggle-input"
                [(ngModel)]="privacySettings.showReadingHistory"
                name="showReadingHistory"
              />
              <span class="toggle-slider"></span>
              Hiển thị lịch sử đọc
            </label>
          </div>

          <div class="form-group">
            <label class="toggle-label">
              <input 
                type="checkbox" 
                class="toggle-input"
                [(ngModel)]="privacySettings.showFavorites"
                name="showFavorites"
              />
              <span class="toggle-slider"></span>
              Hiển thị truyện yêu thích
            </label>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary">Lưu cài đặt</button>
          </div>
        </form>
      </div>

      <!-- Notification Settings -->
      <div *ngIf="activeSection === 'notifications'" class="settings-section">
        <div class="section-header">
          <h3 class="section-title">Cài đặt thông báo</h3>
          <p class="section-subtitle">Quản lý cách bạn nhận thông báo</p>
        </div>

        <form class="settings-form" (ngSubmit)="updateNotificationSettings()">
          <div class="form-group">
            <label class="toggle-label">
              <input 
                type="checkbox" 
                class="toggle-input"
                [(ngModel)]="notificationSettings.emailNotifications"
                name="emailNotifications"
              />
              <span class="toggle-slider"></span>
              Nhận thông báo qua email
            </label>
          </div>

          <div class="form-group">
            <label class="toggle-label">
              <input 
                type="checkbox" 
                class="toggle-input"
                [(ngModel)]="notificationSettings.newChapterAlerts"
                name="newChapterAlerts"
              />
              <span class="toggle-slider"></span>
              Thông báo chương mới
            </label>
          </div>

          <div class="form-group">
            <label class="toggle-label">
              <input 
                type="checkbox" 
                class="toggle-input"
                [(ngModel)]="notificationSettings.favoriteUpdates"
                name="favoriteUpdates"
              />
              <span class="toggle-slider"></span>
              Cập nhật truyện yêu thích
            </label>
          </div>

          <div class="form-group">
            <label class="toggle-label">
              <input 
                type="checkbox" 
                class="toggle-input"
                [(ngModel)]="notificationSettings.systemUpdates"
                name="systemUpdates"
              />
              <span class="toggle-slider"></span>
              Thông báo hệ thống
            </label>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary">Lưu cài đặt</button>
          </div>
        </form>
      </div>

      <!-- Reading Preferences -->
      <div *ngIf="activeSection === 'preferences'" class="settings-section">
        <div class="section-header">
          <h3 class="section-title">Tùy chỉnh đọc truyện</h3>
          <p class="section-subtitle">Cài đặt hiển thị khi đọc truyện</p>
        </div>

        <form class="settings-form" (ngSubmit)="updateReadingPreferences()">
          <div class="form-group">
            <label class="form-label">Giao diện</label>
            <div class="radio-group">
              <label class="radio-label">
                <input type="radio" name="theme" value="light" [(ngModel)]="readingPreferences.theme" />
                <span class="radio-custom"></span>
                Sáng
              </label>
              <label class="radio-label">
                <input type="radio" name="theme" value="dark" [(ngModel)]="readingPreferences.theme" />
                <span class="radio-custom"></span>
                Tối
              </label>
              <label class="radio-label">
                <input type="radio" name="theme" value="auto" [(ngModel)]="readingPreferences.theme" />
                <span class="radio-custom"></span>
                Tự động
              </label>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">Cỡ chữ</label>
            <select class="form-select" [(ngModel)]="readingPreferences.fontSize" name="fontSize">
              <option value="small">Nhỏ</option>
              <option value="medium">Vừa</option>
              <option value="large">Lớn</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Phông chữ</label>
            <select class="form-select" [(ngModel)]="readingPreferences.fontFamily" name="fontFamily">
              <option value="default">Mặc định</option>
              <option value="serif">Serif</option>
              <option value="sans-serif">Sans Serif</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Khoảng cách dòng</label>
            <select class="form-select" [(ngModel)]="readingPreferences.lineHeight" name="lineHeight">
              <option value="compact">Nhỏ gọn</option>
              <option value="normal">Bình thường</option>
              <option value="relaxed">Thoải mái</option>
            </select>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary">Lưu cài đặt</button>
          </div>
        </form>
      </div>

      <!-- Danger Zone -->
      <div class="danger-zone">
        <h3 class="danger-title">Vùng nguy hiểm</h3>
        <p class="danger-subtitle">Các hành động này không thể hoàn tác</p>
        <button class="btn btn-danger" (click)="deleteAccount()">Xóa tài khoản</button>
      </div>
    </div>
  </div>
</div>
