.category-content {
  @apply min-h-screen bg-gray-50 dark:bg-dark-background;
}

// Genre Header
.genre-header {
  @apply bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-white py-12 lg:py-16;
}

.header-content {
  @apply max-w-6xl mx-auto px-4 text-center;
}

.genre-title {
  @apply text-2xl lg:text-4xl font-bold mb-4 leading-tight;
}

.genre-description {
  @apply text-lg lg:text-xl mb-6 max-w-4xl mx-auto leading-relaxed opacity-90;
}

.genre-stats {
  @apply flex justify-center gap-6 flex-wrap;
}

.stat-item {
  @apply text-center;
}

.stat-number {
  @apply block text-xl lg:text-2xl font-bold text-yellow-300;
}

.stat-label {
  @apply block text-sm opacity-80;
}

// Sections
.section-header {
  @apply text-center mb-8;
}

.section-title {
  @apply text-xl lg:text-2xl font-bold text-gray-900 dark:text-white mb-2;
}

.section-description {
  @apply text-gray-600 dark:text-gray-300 max-w-2xl mx-auto;
}

// Tags Section
.tags-section {
  @apply py-8 max-w-6xl mx-auto px-4;
}

.tags-container {
  @apply flex flex-wrap gap-2 justify-center;
}

.tag-item {
  @apply bg-blue-100 dark:bg-dark-600 text-blue-800 dark:text-blue-300 px-3 py-1 rounded-full text-sm font-medium;
}

// Novel Sections
.featured-novels,
.top-novels,
.new-novels,
.completed-novels {
  @apply py-8 max-w-6xl mx-auto px-4;
  
  &:nth-child(even) {
    @apply bg-white dark:bg-dark-800;
  }
}

.novels-grid {
  @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4;
}

.novel-item {
  @apply transition-transform duration-200 hover:scale-105;
}

// FAQ Section
.genre-faq {
  @apply py-12 bg-white dark:bg-dark-800;
}

// CTA Section
.cta-section {
  @apply py-12 bg-gradient-to-r from-indigo-600 to-purple-600 text-white;
}

.cta-content {
  @apply max-w-4xl mx-auto px-4 text-center;
}

.cta-title {
  @apply text-xl lg:text-2xl font-bold mb-3;
}

.cta-description {
  @apply text-base lg:text-lg mb-6 opacity-90;
}

.cta-actions {
  @apply flex justify-center gap-4 flex-wrap;
}

// Buttons
.btn-primary {
  @apply bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold px-6 py-2 rounded-lg transition-colors duration-200 inline-block;
}

.btn-secondary {
  @apply bg-transparent border-2 border-white hover:bg-white hover:text-gray-900 text-white font-semibold px-6 py-2 rounded-lg transition-colors duration-200 inline-block;
}

// Responsive Design
@media (max-width: 640px) {
  .genre-title {
    @apply text-xl;
  }
  
  .genre-description {
    @apply text-base;
  }
  
  .genre-stats {
    @apply gap-4;
  }
  
  .stat-number {
    @apply text-lg;
  }
  
  .novels-grid {
    @apply grid-cols-2 gap-3;
  }
  
  .tags-container {
    @apply gap-1;
  }
  
  .tag-item {
    @apply text-xs px-2;
  }
  
  .btn-primary,
  .btn-secondary {
    @apply px-4 py-2 text-sm;
  }
}
