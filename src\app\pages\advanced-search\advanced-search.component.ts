import { isPlatformServer } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IFilterOptions } from '@components/utils/constants';
import { NovelStatus, SortType, TranslationType, WordCountRange } from '@schemas/enum';
import { Novel } from '@schemas/Novel';
import { NovelList } from '@schemas/NovelList';
import { IServiceResponse } from '@schemas/ResponseType';
import { NovelService } from '@services/novel.service';
import { SeoService } from '@services/seo.service';
import globalConfig from 'globalConfig';
import { combineLatest, Subscription } from 'rxjs';
@Component({
  selector: 'app-advanced-search',
  standalone: false,
  templateUrl: './advanced-search.component.html',
  styleUrl: './advanced-search.component.scss',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class AdvancedSearchComponent {
  selectedValues: IFilterOptions = {
    sort: -1,
    status: -1,
    translation: -1,
    wordcount: -1,
    keyword: "",
    genres: "",
    nogenres: "",
  };
  completeNovels: Novel[] = [];
  girdType = 1
  length = 0
  totalpage = 0;
  currentPage = 1;
  step = 20;
  isLoading: boolean = true
  constructor(
    private novelService: NovelService,
    private router: Router,
    private seoService: SeoService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
  }
  subject$: Subscription | undefined;
  ngOnInit() {
    this.isLoading = true;
    const combined$ = combineLatest([
      this.route.paramMap,
      this.route.queryParamMap
    ]);
    this.subject$ = combined$.subscribe(([params, queryParams]) => {
      const page = params.get('page')?.replace('trang-', '') || 1;
      if (!Number.isInteger(Number(page))) {
        this.router.navigate(['/not-found'])
        return;
      }
      const status = +(queryParams.get('status') ?? NovelStatus.ALL);
      const sort = +(queryParams.get('sort') ?? SortType.LastUpdate);
      const genres = queryParams.get('genres') || '';
      const nogenres = queryParams.get('nogenres') || '';
      const wordcount = +(queryParams.get('wordcount') ?? WordCountRange.ALL);
      const translation = +(queryParams.get('translation') ?? TranslationType.ALL);
      const keyword = queryParams.get('keyword') || '';

      this.currentPage = Number(page);
      this.selectedValues = {
        ...this.selectedValues,
        status,
        sort,
        genres,
        nogenres,
        wordcount,
        translation,
        keyword
      };

      this.searchNovels(this.currentPage, sort, status, genres, nogenres, wordcount, translation, keyword);
    });
  }

  ngOnDestroy(): void {
    this.subject$?.unsubscribe();
  }

  searchNovels(page: number,
    sort: number,
    status: number,
    genres: string,
    nogenres: string,
    wordcount: number,
    translation: number,
    keyword: string,) {

    this.isLoading = true;

    this.novelService.getAdvanceSearchNovel(page, this.step, sort, status, genres, nogenres, translation, wordcount, keyword).subscribe((res: IServiceResponse<NovelList>) => {
      if (res.data)
        this.completeNovels = res.data?.novels
      this.length = this.completeNovels.length
      this.totalpage = res.data?.totalpage || 0;
      this.currentPage = res.data?.page || 1;
      this.isLoading = false;
      
      // Update SEO again with search results information
      this.updateSeoWithResults(page, keyword, status, sort, genres, nogenres, wordcount, translation, this.totalpage*this.step, this.totalpage);
      
      this.cd.markForCheck()
    });


  }

  executeSearch(data: IFilterOptions) {
    this.selectedValues = data
    let queryParams: { [key: string]: any } = {};
    if (this.selectedValues.keyword !== "") {
      queryParams['keyword'] = this.selectedValues.keyword
    }
    if (this.selectedValues.sort !== 1) {
      queryParams['sort'] = this.selectedValues.sort
    }
    if (this.selectedValues.status !== -1) {
      queryParams['status'] = this.selectedValues.status
    }
    if (this.selectedValues.translation !== -1) {
      queryParams['translation'] = this.selectedValues.translation
    }
    if (this.selectedValues.wordcount !== -1) {
      queryParams['wordcount'] = this.selectedValues.wordcount
    }
    if (this.selectedValues.genres !== "") {
      queryParams['genres'] = this.selectedValues.genres
    }
    if (this.selectedValues.nogenres !== "") {
      queryParams['nogenres'] = this.selectedValues.nogenres
    }

    this.router.navigate(['/tim-kiem'], {
      queryParams: queryParams,
      queryParamsHandling: 'replace',
      fragment: 'listNovel',

    });
  }
  ChangeGridType(target: any, type: number) {
    if (this.girdType == type) return;

    this.girdType = type;
  }

  isServer() {
    return isPlatformServer(this.platformId);
  }

  /**
   * Update SEO with search results information
   */
  updateSeoWithResults(
    page: number,
    keyword: string = '',
    status: number = -1,
    sort: number = -1,
    genres: string = '',
    nogenres: string = '',
    wordcount: number = -1,
    translation: number = -1,
    resultCount: number = 0,
    totalPages: number = 0
  ) {
    // Build dynamic title based on filters, page, and results
    let title = 'Tìm Kiếm Truyện Chữ Online';
    let description = 'Tìm kiếm truyện chữ online miễn phí với bộ lọc thông minh theo thể loại, tác giả, trạng thái.';
    
    // Add keyword to title if exists
    if (keyword) {
      title = `Tìm Kiếm: "${keyword}" - Truyện Chữ Online`;
      description = `Tìm thấy ${resultCount > 0 ? `${resultCount} kết quả` : 'không có kết quả'} cho "${keyword}". ${description}`;
    } else if (resultCount > 0) {
      description = `Tìm thấy ${resultCount} truyện phù hợp. ${description}`;
    }
    
    // Add page number to title if not page 1
    if (page > 1) {
      title += ` - Trang ${page}`;
      description = `Trang ${page} của ${totalPages}. ${description}`;
    } else if (totalPages > 1) {
      description += ` Tổng cộng ${totalPages} trang.`;
    }
    
    // Add filter information to title
    const filterInfo = this.buildFilterInfo(status, sort, genres, nogenres, wordcount, translation);
    if (filterInfo) {
      title += ` ${filterInfo}`;
    }
    
    // Build URL with current filters
    const url = this.buildCurrentUrl(page, keyword, status, sort, genres, nogenres, wordcount, translation);
    
    const seoData = {
      title,
      description,
      type: 'website' as const,
      image: `${globalConfig.BASE_URL}/logo.png`,
      url,
      siteName: globalConfig.APP_NAME,
      locale: 'vi_VN',
      twitterCard: 'summary' as const,
      keywords: this.buildKeywords(keyword, genres),
      canonical: url
    };

    this.seoService.setSEOData(seoData);

    // Enhanced search results schema with result count
    const searchSchema = {
      '@context': 'https://schema.org',
      '@type': 'SearchResultsPage',
      'name': title,
      'description': description,
      'url': url,
      'inLanguage': 'vi-VN',
      'numberOfItems': resultCount,
      'isPartOf': {
        '@type': 'WebSite',
        'name': globalConfig.APP_NAME,
        'url': globalConfig.BASE_URL
      },
      'potentialAction': {
        '@type': 'SearchAction',
        'target': {
          '@type': 'EntryPoint',
          'urlTemplate': `${globalConfig.BASE_URL}/tim-kiem?keyword={search_term_string}`
        },
        'query-input': 'required name=search_term_string'
      }
    };

    // Add pagination schema if there are multiple pages
    const schemas: any[] = [searchSchema];
    
    if (totalPages > 1) {
      const paginationSchema = {
        '@context': 'https://schema.org',
        '@type': 'CollectionPage',
        'name': title,
        'description': description,
        'url': url,
        'numberOfItems': resultCount,
        'inLanguage': 'vi-VN',
        'isPartOf': {
          '@type': 'WebSite',
          'name': globalConfig.APP_NAME,
          'url': globalConfig.BASE_URL
        }
      };
      schemas.push(paginationSchema);
    }

    this.seoService.addStructuredData(schemas);
  }


  private buildFilterInfo(
    status: number,
    sort: number,
    genres: string,
    nogenres: string,
    wordcount: number,
    translation: number
  ): string {
    const filters: string[] = [];
    
    // Add status filter
    if (status !== -1) {
      const statusMap: { [key: number]: string } = {
        [NovelStatus.ONGOING]: 'Đang Ra',
        [NovelStatus.COMPLETED]: 'Hoàn Thành'
      };
      if (statusMap[status]) {
        filters.push(`Trạng Thái: ${statusMap[status]}`);
      }
    }
    
    // Add sort filter
    if (sort !== -1 && sort !== SortType.LastUpdate) {
      const sortMap: { [key: number]: string } = {
        [SortType.NewNovel]: 'Mới Nhất',
        [SortType.TopAll]: 'Lượt Xem',
        [SortType.TopFollow]: 'Yêu Thích',
        [SortType.TopComment]: 'Bình Luận',
        [SortType.Chapter]: 'Số Chương'
      };
      if (sortMap[sort]) {
        filters.push(`Sắp Xếp: ${sortMap[sort]}`);
      }
    }
    
    // Add genres
    if (genres) {
      filters.push(`Thể Loại: ${genres.split(',').slice(0, 2).join(', ')}`);
    }
    
    // Add translation type
    if (translation !== -1) {
      const translationMap: { [key: number]: string } = {
        [TranslationType.Machine]: 'Dịch Máy',
        [TranslationType.Human]: 'Dịch Người'
      };
      if (translationMap[translation]) {
        filters.push(`Loại: ${translationMap[translation]}`);
      }
    }
    
    return filters.length > 0 ? `(${filters.slice(0, 2).join(', ')})` : '';
  }

  /**
   * Build current URL with all parameters
   */
  private buildCurrentUrl(
    page: number,
    keyword: string,
    status: number,
    sort: number,
    genres: string,
    nogenres: string,
    wordcount: number,
    translation: number
  ): string {
    let url = `${globalConfig.BASE_URL}/tim-kiem`;
    
    // Add page to URL if not page 1
    if (page > 1) {
      url += `/trang-${page}`;
    }
    
    // Build query parameters
    const queryParams: string[] = [];
    
    if (keyword) queryParams.push(`keyword=${encodeURIComponent(keyword)}`);
    if (sort !== -1 && sort !== SortType.LastUpdate) queryParams.push(`sort=${sort}`);
    if (status !== -1) queryParams.push(`status=${status}`);
    if (translation !== -1) queryParams.push(`translation=${translation}`);
    if (wordcount !== -1) queryParams.push(`wordcount=${wordcount}`);
    if (genres) queryParams.push(`genres=${encodeURIComponent(genres)}`);
    if (nogenres) queryParams.push(`nogenres=${encodeURIComponent(nogenres)}`);
    
    if (queryParams.length > 0) {
      url += `?${queryParams.join('&')}`;
    }
    
    return url;
  }

  /**
   * Build dynamic keywords for SEO
   */
  private buildKeywords(keyword: string, genres: string): string {
    const baseKeywords = 'tìm kiếm truyện, bộ lọc truyện chữ, danh sách truyện, truyện theo thể loại, tìm truyện hay, lọc truyện online';
    
    const dynamicKeywords: string[] = [];
    
    if (keyword) {
      dynamicKeywords.push(keyword);
      dynamicKeywords.push(`truyện ${keyword}`);
    }
    
    if (genres) {
      const genreList = genres.split(',').slice(0, 3);
      genreList.forEach(genre => {
        if (genre.trim()) {
          dynamicKeywords.push(`truyện ${genre.trim()}`);
        }
      });
    }
    
    return dynamicKeywords.length > 0 
      ? `${dynamicKeywords.join(', ')}, ${baseKeywords}`
      : baseKeywords;
  }


}
