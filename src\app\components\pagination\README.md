# Pagination Component

Một component phân trang được tối ưu hóa cho website truyện chữ với giao diện đẹp và hiệu suất cao.

## Tính năng

### 🎨 Giao diện
- Design hiện đại phù hợp với theme website truyện chữ
- Responsive design cho mobile và desktop
- Dark mode support
- Smooth animations và transitions
- Hover effects và visual feedback

### ⚡ Hiệu suất
- OnPush change detection strategy
- TrackBy functions cho ngFor
- Lazy loading animations
- Optimized re-rendering

### 🎮 Tương tác
- **Keyboard Navigation**:
  - `←` / `→`: Điều hướng trang trước/sau
  - `/`: Mở ô tìm kiếm trang
  - `Enter`: Xác nhận tìm kiếm
  - `Esc`: Đóng ô tìm kiếm

- **Mouse/Touch**:
  - Click các số trang để điều hướng
  - Click "..." để mở ô tìm kiếm nhanh
  - Hover effects trên tất cả elements

### 🔍 Tìm kiếm trang
- Input validation thời gian thực
- Auto-focus và text selection
- Enter để confirm, Esc để cancel
- Visual feedback cho invalid input

### ♿ Accessibility
- Proper ARIA labels và roles
- Keyboard navigation support
- Screen reader friendly
- Focus management

## Cách sử dụng

### Basic Usage
```html
<app-pagination
  [currentPage]="currentPage"
  [totalpage]="totalPages"
  [rootLink]="'/truyen'"
  (OnChange)="onPageChange($event)">
</app-pagination>
```

### Advanced Usage
```html
<app-pagination
  [currentPage]="currentPage"
  [totalpage]="totalPages"
  [rootLink]="'/danh-sach'"
  [queryParams]="{ sort: 'newest', genre: 'fantasy' }"
  (OnChange)="handlePageChange($event)">
</app-pagination>
```

## Inputs

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `currentPage` | `number` | `1` | Trang hiện tại |
| `totalpage` | `number` | Required | Tổng số trang |
| `rootLink` | `string` | `''` | Base URL cho navigation |
| `queryParams` | `Params` | `{}` | Query parameters để preserve |

## Outputs

| Event | Type | Description |
|-------|------|-------------|
| `OnChange` | `EventEmitter<number>` | Emit khi user chọn trang mới |

## Logic hiển thị

### Ít hơn 6 trang
Hiển thị tất cả: `1 2 3 4 5`

### Trang đầu (1-3)
`1 2 3 4 ... 100`

### Trang giữa
`1 ... 45 46 47 ... 100`

### Trang cuối
`1 ... 97 98 99 100`

## Customization

### CSS Variables
Component sử dụng các CSS custom properties từ theme:
- `--color-primary-100`: Primary color
- `--color-primary-200`: Primary hover color

### SCSS Classes
Tất cả styles có thể được override:
```scss
.pagination-container { /* Container chính */ }
.pagination-nav { /* Navigation wrapper */ }
.pagination-page { /* Số trang */ }
.pagination-page.active { /* Trang hiện tại */ }
.pagination-btn { /* Nút Trước/Tiếp */ }
.pagination-search { /* Box tìm kiếm */ }
```

## Performance Tips

1. **Sử dụng OnPush**: Component đã implement OnPush change detection
2. **TrackBy function**: Đã có sẵn để tối ưu ngFor
3. **Lazy animations**: Animations chỉ chạy khi cần thiết
4. **Debounced input**: Input validation được debounce

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Dependencies

- Angular 15+
- Angular Animations
- Angular Router

## Changelog

### v2.0.0
- Redesigned UI phù hợp với theme website truyện chữ
- Thêm keyboard navigation
- Cải thiện accessibility
- Tối ưu hiệu suất với OnPush
- Thêm search functionality
- Mobile responsive improvements
- Dark mode support
- Smooth animations
