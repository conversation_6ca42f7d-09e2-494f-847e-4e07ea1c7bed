import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, ChangeDetectionStrategy, ViewEncapsulation } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NovelCardV1Component } from '@components/novel-card-v1/novel-card-v1.component';
import { Novel } from '@schemas/Novel';
import { Genre } from '@schemas/Genre';

export interface RelatedSection {
  title: string;
  novels: Novel[];
  viewAllLink?: string;
  description?: string;
}

@Component({
  selector: 'app-related-content',
  templateUrl: './related-content.component.html',
  styleUrls: ['./related-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule, 
    RouterLink, 
    NovelCardV1Component
  ]
})
export class RelatedContentComponent implements OnInit {
  @Input() currentNovel?: Novel;
  @Input() currentGenre?: Genre;
  @Input() relatedSections: RelatedSection[] = [];
  @Input() showSimilarNovels: boolean = true;
  @Input() showSameAuthor: boolean = true;
  @Input() showSameGenre: boolean = true;
  @Input() maxItemsPerSection: number = 6;

  constructor() {}

  ngOnInit() {
    // Component initialization
  }

  trackByNovel(index: number, novel: Novel): number {
    return novel.id;
  }

  trackBySection(index: number, section: RelatedSection): string {
    return section.title;
  }

  getSectionDescription(section: RelatedSection): string {
    if (section.description) {
      return section.description;
    }

    // Generate default descriptions based on section title
    const title = section.title.toLowerCase();
    
    if (title.includes('cùng tác giả')) {
      return `Khám phá thêm những tác phẩm khác của tác giả ${this.currentNovel?.author}`;
    } else if (title.includes('cùng thể loại')) {
      return `Những bộ truyện ${this.currentGenre?.title} hay không thể bỏ qua`;
    } else if (title.includes('tương tự')) {
      return `Những bộ truyện có nội dung và phong cách tương tự`;
    } else if (title.includes('đề xuất')) {
      return 'Những bộ truyện được đề xuất dành riêng cho bạn';
    }
    
    return 'Khám phá thêm những bộ truyện hay khác';
  }

  hasValidNovels(section: RelatedSection): boolean {
    return section.novels && section.novels.length > 0;
  }

  getDisplayNovels(section: RelatedSection): Novel[] {
    if (!this.hasValidNovels(section)) {
      return [];
    }
    
    return section.novels.slice(0, this.maxItemsPerSection);
  }

  shouldShowViewAll(section: RelatedSection): boolean {
    return !!(section.viewAllLink && section.novels.length > this.maxItemsPerSection);
  }
}
