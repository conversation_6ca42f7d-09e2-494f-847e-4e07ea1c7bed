<div class="genre-page" *ngIf="genre">
  <!-- Breadcrumb Navigation -->
  <nav class="breadcrumb-nav" aria-label="Breadcrumb">
    <ol class="breadcrumb-list">
      <li *ngFor="let link of breadcrumbLinks; let last = last" class="breadcrumb-item">
        <a *ngIf="link.url; else textOnly"
           [routerLink]="link.url"
           class="breadcrumb-link">
          {{ link.label }}
        </a>
        <ng-template #textOnly>
          <span class="breadcrumb-current">{{ link.label }}</span>
        </ng-template>
        <span *ngIf="!last" class="breadcrumb-separator">/</span>
      </li>
    </ol>
  </nav>

  <!-- Genre Header -->
  <header class="genre-header">
    <div class="header-content">
      <h1 class="genre-title">Truyện {{ genre.title }} Hay Nhất</h1>
      <p class="genre-description">{{ genre.description }}</p>

      <div class="genre-stats" *ngIf="genreData.totalCount > 0">
        <div class="stat-item">
          <span class="stat-number">{{ genreData.totalCount | number }}</span>
          <span class="stat-label">Bộ truyện</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">24/7</span>
          <span class="stat-label">Cập nhật</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">100%</span>
          <span class="stat-label">Miễn phí</span>
        </div>
      </div>
    </div>
  </header>

  <!-- Filter Controls -->
  <section class="filter-section">
    <div class="filter-container">
      <div class="filter-group">
        <label class="filter-label">Sắp xếp theo:</label>
        <select
          class="filter-select"
          [value]="sortBy"
          (change)="onSortChange($any($event.target).value)"
        >
          <option value="update">Cập nhật mới nhất</option>
          <option value="view">Lượt xem cao nhất</option>
          <option value="rating">Đánh giá cao nhất</option>
          <option value="chapter">Nhiều chương nhất</option>
        </select>
      </div>

      <div class="filter-group">
        <label class="filter-label">Trạng thái:</label>
        <select
          class="filter-select"
          [value]="status"
          (change)="onStatusChange($any($event.target).value)"
        >
          <option value="all">Tất cả</option>
          <option value="ongoing">Đang tiến hành</option>
          <option value="completed">Đã hoàn thành</option>
        </select>
      </div>

      <div class="results-info">
        <span class="results-text">
          Hiển thị {{ genreData.novels.length }} / {{ genreData.totalCount }} truyện
        </span>
      </div>
    </div>
  </section>

  <!-- Loading State -->
  <div *ngIf="loading$ | async" class="loading-container">
    <div class="loading-spinner"></div>
    <p class="loading-text">Đang tải truyện...</p>
  </div>

  <!-- Novels Grid -->
  <section class="novels-section" *ngIf="!(loading$ | async) && genreData.novels.length > 0">
    <div class="novels-grid">
      <app-card-v1
        *ngFor="let novel of genreData.novels; trackBy: trackByNovel"
        [novel]="novel"
        class="novel-item"
      ></app-card-v1>
    </div>

    <!-- Pagination -->
    <nav class="pagination-nav" *ngIf="genreData.totalPages > 1" aria-label="Pagination">
      <div class="pagination-container">
        <!-- Previous Page -->
        <button
          class="pagination-btn"
          [class.disabled]="genreData.currentPage <= 1"
          [disabled]="genreData.currentPage <= 1"
          (click)="loadPage(genreData.currentPage - 1)"
          aria-label="Trang trước"
        >
          ← Trước
        </button>

        <!-- Page Numbers -->
        <div class="page-numbers">
          <ng-container *ngFor="let page of getPaginationPages()">
            <button
              *ngIf="page !== '...'; else ellipsis"
              class="page-btn"
              [class.active]="page === genreData.currentPage"
              (click)="loadPage(+page)"
              [attr.aria-label]="'Trang ' + page"
            >
              {{ page }}
            </button>
            <ng-template #ellipsis>
              <span class="page-ellipsis">...</span>
            </ng-template>
          </ng-container>
        </div>

        <!-- Next Page -->
        <button
          class="pagination-btn"
          [class.disabled]="genreData.currentPage >= genreData.totalPages"
          [disabled]="genreData.currentPage >= genreData.totalPages"
          (click)="loadPage(genreData.currentPage + 1)"
          aria-label="Trang sau"
        >
          Sau →
        </button>
      </div>
    </nav>
  </section>

  <!-- Empty State -->
  <div *ngIf="!(loading$ | async) && genreData.novels.length === 0" class="empty-state">
    <div class="empty-content">
      <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
        <polyline points="14,2 14,8 20,8"></polyline>
        <line x1="16" y1="13" x2="8" y2="13"></line>
        <line x1="16" y1="17" x2="8" y2="17"></line>
        <polyline points="10,9 9,9 8,9"></polyline>
      </svg>
      <h3 class="empty-title">Không tìm thấy truyện nào</h3>
      <p class="empty-description">
        Hiện tại chưa có truyện {{ genre.title }} nào. Hãy thử lại sau hoặc khám phá các thể loại khác.
      </p>
      <a routerLink="/" class="empty-action">
        Khám phá truyện hay →
      </a>
    </div>
  </div>

  <!-- FAQ Section -->
  <app-faq
    [faqs]="getGenreFAQs()"
    [title]="'Câu Hỏi Về Truyện ' + genre.title"
    [showStructuredData]="true"
    class="genre-faq"
  ></app-faq>
</div>
