import { Component, Input, On<PERSON><PERSON>roy } from '@angular/core';
import { ImageService } from '@services/image.service';

@Component({
  selector: 'app-image-loader',
  templateUrl: './image-loader.component.html',
  styleUrls: ['./image-loader.component.scss'],
})
export class ImageLoaderComponent implements OnDestroy {
  @Input()
  imageUrl!: string;
  imageSrc: string = '';
  @Input() alt: string = 'image';
  @Input() vertical!: boolean;
  @Input() isNightMode!: boolean;

  constructor(private imageService: ImageService) { }

  ngOnInit() {
    this.loadImage();
  }

  loadImage(): void {
    if (!this.imageUrl) return;
    this.imageSrc = this.imageUrl;
  }

  ngOnDestroy() {
    this.imageService.CancelLoad(this.imageUrl);
  }
}
