import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BreadcrumbComponent } from '@components/breadcrumb/breadcrumb.component';
import { ChapterListComponent } from '@components/chapter-list/chapter-list.component';
import { CommentModule } from '@components/comment/comment.module';
import { AuthorIconComponent } from '@components/icons/author/author.component';
import { NovelCardV1Component } from '@components/novel-card/card-v1/card-v1.component';
import { NovelCardV2Component } from '@components/novel-card/card-v2/card-v2.component';
import { NumeralPipe } from 'src/app/shared/pines/numeral.pipe';
import { PinesModule } from 'src/app/shared/pines/pines.module';
import { ReadTimePipe } from 'src/app/shared/pines/read-time.pine';
import { DetailComponent } from './detail.component';

const routes: Routes = [
  { path: '', component: DetailComponent },
];

@NgModule({
  declarations: [DetailComponent],
  imports: [
    CommonModule,
    ReadTimePipe,
    RouterModule.forChild(routes),
    PinesModule,
    BreadcrumbComponent,
    ChapterListComponent,
    NovelCardV1Component,
    NovelCardV2Component,
    CommentModule,
    NumeralPipe,
    AuthorIconComponent,
  ],
})
export class NovelModule { }
