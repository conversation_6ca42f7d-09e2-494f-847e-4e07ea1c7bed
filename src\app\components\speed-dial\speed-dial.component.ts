import { CommonModule } from '@angular/common';
import { Component, ContentChild, EventEmitter, Input, Output, TemplateRef, ViewEncapsulation } from '@angular/core';
import { ClickOutsideDirective } from '@directives/click-outside.directive';

@Component({
  selector: 'app-speed-dial',
  templateUrl: './speed-dial.component.html',
  styleUrl: './speed-dial.component.scss',
  imports: [CommonModule, ClickOutsideDirective],
  encapsulation: ViewEncapsulation.None,
})
export class SpeedDialComponent {
  @Input() items: { icon: string, action: () => void }[] = [];
  isOpened = false;
  @Output() clickEvent = new EventEmitter<number>();

  @ContentChild("itemTemplate") itemTemplate: TemplateRef<any> | undefined;

  toggleMenu() {
    this.isOpened = !this.isOpened;
  }
  buttonClick(index: number) {
    this.isOpened = false;
    this.clickEvent.emit(index);
  }
}