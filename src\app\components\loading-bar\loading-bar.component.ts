import { CommonModule, isPlatformServer } from '@angular/common';
import {
  AfterViewChecked,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Inject,
  OnInit,
  PLATFORM_ID,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import {
  NavigationCancel,
  NavigationEnd,
  NavigationError,
  NavigationStart,
  ResolveEnd,
  Router
} from '@angular/router';
import { LoadingService } from '@services/loading.service';
import { timer } from 'rxjs';

@Component({
  selector: 'app-loading-bar',
  templateUrl: './loading-bar.component.html',
  styleUrls: ['./loading-bar.component.scss'],
  imports: [CommonModule],
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoadingBarComponent implements OnInit, AfterViewChecked {
  @ViewChild('ProgressBar') ProgressBar!: ElementRef;
  isLoading = false;
  isDone = false;
  maxtask = 0;
  timer1: any;
  timer2: any;
  progress = 0;
  navigationEnd = false;
  constructor(
    public loadingService: LoadingService,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object,
    private cd: ChangeDetectorRef
  ) { }
  ngOnInit(): void {
    this.router.events.subscribe((event) => {
      if (isPlatformServer(this.platformId)) return;
      if (event instanceof NavigationStart) {
        this.isLoading = true;
        this.isDone = false;
        this.progress = 5;
        this.navigationEnd = false;
        this.clearTimer();
      }

      if (event instanceof NavigationCancel || event instanceof NavigationError) {
        this.isLoading = false;

        this.clearTimer();
      }

      let x = event instanceof ResolveEnd ? 5 : 1;
      this.progress += x;
      this.ProgressBar.nativeElement.style.width = `${this.progress}%`;

      if (event instanceof NavigationEnd) {
        this.maxtask = this.loadingService.tasks.length;
        this.navigationEnd = true;        
        this.UpdateProgress();

      }
      this.cd.markForCheck();
    });

  }
  clearTimer() {
    if (this.timer1) clearTimeout(this.timer1);
    if (this.timer2) clearTimeout(this.timer2);
    this.timer1 = null;
    this.timer2 = null;
  }



  UpdateProgress() {
    let taskLength = this.loadingService.tasks.length;
    let value = ((this.maxtask - taskLength) / this.maxtask) * (80 - this.progress);
    this.ProgressBar.nativeElement.style.width = `${this.progress + value}%`;

    if (!this.loadingService.hasTasks()) {
      this.isDone = true;
      this.timer1 = timer(200).subscribe(() => {
        this.DoneLoading();
        this.timer1 = null;
      });

      return;
    }
  }
  ngAfterViewChecked() {
    if (isPlatformServer(this.platformId)) return;
    if (!this.navigationEnd) return;
    if (!this.isDone) {
      this.UpdateProgress();
      this.cd.markForCheck();

    }

  }
  DoneLoading() {
    this.ProgressBar.nativeElement.style.width = '100%';
    this.timer2 = timer(500).subscribe(() => {
      this.isLoading = false;
      this.timer2 = null;
      this.cd.markForCheck();

    });

  }
}
