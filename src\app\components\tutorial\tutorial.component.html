<div
  #tutorial
  class="flex flex-col fixed w-72 h-64 bg-black/50 p-2 rounded-md z-50"
  [style]="{
    left: position.x + 'px',
    top: position.y + 'px',
    transform: 'translate(-50%, 50px)'
  }"
>
  <h3 class="px-3 py-1 rounded bg-white text-gray-900 font-semibold text-lg">
    Thanh công cụ là gì?
  </h3>
  <div class="py-3 h-full flex mx-auto overflow-hidden">
    <p class="text-base text-white font-normal">
      <PERSON>h công cụ là một nhóm các hành vi để giúp đọc tiên lợi hơn: bao gồm
      chuyển chapter, tự động cuộn, toàn màn hình, danh sách chapter, nghe, cài
      đặt
    </p>
  </div>
  <div class="flex-between gap-3 px-3 bg-white rounded">
    <button
      (click)="this.back.emit()"
      class="flex-start gap-1.5 border-none text-base font-medium py-2.5 text-gray-700 transition-all duration-300 hover:text-indigo-600"
      aria-label="Back to previous chapter"
      >
      <svg
        class="rotate-180"
        xmlns="http://www.w3.org/2000/svg"
        width="22"
        height="23"
        viewBox="0 0 22 23"
        fill="none"
      >
        <path
          d="M8.25324 6.37646L13.7535 11.8767L8.25 17.3802"
          stroke="currentColor"
          stroke-width="1.6"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
      Back
    </button>
    <ul class="flex gap-1 items-center">
      <li class="text-lg font-medium text-gray-900">1</li>
      <li class="text-base font-normal text-gray-900">/</li>
      <li class="text-base font-normal text-gray-600">3</li>
    </ul>
    <button
      (click)="this.next.emit()"
      class="flex-start gap-1.5 border-none text-base font-medium py-2.5 text-gray-700 transition-all duration-300 hover:text-indigo-600"
      aria-label="Next chapter"
      >
      Next<svg
        xmlns="http://www.w3.org/2000/svg"
        width="22"
        height="23"
        viewBox="0 0 22 23"
        fill="none"
      >
        <path
          d="M8.25324 6.37646L13.7535 11.8767L8.25 17.3802"
          stroke="currentColor"
          stroke-width="1.6"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>
  </div>
</div>
