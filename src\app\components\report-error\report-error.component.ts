import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { openDialog } from '@components/utils/animation';
import { Novel } from '@schemas/Novel';
import { AccountService } from '@services/account.service';
import { ReportErrorService } from '@services/reportError.service';
import { ToastService, ToastType } from '@services/toast.service';
import { IPopupComponent } from 'src/app/core/interface';

@Component({
    templateUrl: './report-error.component.html',
    styleUrl: './report-error.component.scss',
    imports: [CommonModule, ReactiveFormsModule],
    animations: [openDialog  ]
})
export class ReportErrorComponent  {
  @Input() isShow = false;
  errorForm: FormGroup;
  novel?: Novel;
  defaultType: string[] = ['Lỗi audio', 'Truyện tải chậm', 'Lỗi hiển thị', 'khác'];

  constructor(
    private fb: FormBuilder,
    private toastService: ToastService,
    private reportErrorService: ReportErrorService,
    private accountService: AccountService,
  ) {
    this.errorForm = this.fb.group({
      errorType: [
        '',
        Validators.compose([Validators.required, Validators.maxLength(1000)]),
      ],
      message: ['', Validators.maxLength(1000)],
    });
  }
  open(novel : Novel) {
    this.isShow = true;
  }

  sendReportError() {
    if (this.errorForm.valid) {
      const { errorType, message } = this.errorForm.value;
      const user = this.accountService.GetUser();
      const name = user?.email || '<EMAIL>';
      this.toastService.show(
        ToastType.Info,
        'Cảm ơn bạn đã báo lỗi, team sẽ xác nhận và cải thiện nhé!',
      );

      this.reportErrorService
        .sendReport({
          name,
          errorType,
          message,
          novelId: this.novel?.id,
        })
        .subscribe({
          next: () => {
            this.toastService.show(ToastType.Success, 'Gửi báo cáo thành công');
            this.isShow = false;
            this.errorForm.reset();
          },
          error: (error) => {
            this.toastService.show(
              ToastType.Error,
              'Gửi báo cáo thất bại, đã có lỗi xảy ra',
            );
            this.isShow = false;
            console.error('Error sending report', error);
          },
        });
    } else {
      console.error('Form is invalid');
      this.isShow = false;
      this.toastService.show(
        ToastType.Error,
        'Gửi thất báo cáo thất bại, đã có lỗi xảy ra',
      );
      return;
    }
    this.isShow = false;
  }

  cancelReportError() {
    this.isShow = false;
  }

  setErrorType(tag: string) {
    this.errorForm.controls['errorType'].setValue(tag);
  }
}
