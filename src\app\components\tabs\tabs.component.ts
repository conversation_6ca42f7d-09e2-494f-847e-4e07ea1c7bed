import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { PinesModule } from "../../shared/pines/pines.module";

@Component({
  selector: 'app-tabs',
  imports: [CommonModule, PinesModule],
  templateUrl: './tabs.component.html',
  styleUrl: './tabs.component.scss'
})
export class TabsComponent {
  @Input() selectedTab = 0
  @Output() selectedTabChange = new EventEmitter<number>();
  @Input()
  tabs : any[] = [];
  selectTab(tabId: number) {
    this.selectedTab = tabId;
    this.selectedTabChange.emit(this.selectedTab);
  }

}
