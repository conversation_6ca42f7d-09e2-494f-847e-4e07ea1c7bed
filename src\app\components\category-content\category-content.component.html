<div class="category-content" *ngIf="genre">
  <!-- Genre Header -->
  <section class="genre-header">
    <div class="header-content">
      <h1 class="genre-title">Truyện {{ genre.title }} Hay Nhất</h1>
      <p class="genre-description">{{ getGenreDescription() }}</p>
      
      <div class="genre-stats" *ngIf="totalNovels > 0">
        <div class="stat-item">
          <span class="stat-number">{{ totalNovels | number }}</span>
          <span class="stat-label">Bộ truyện</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">24/7</span>
          <span class="stat-label">Cập nhật</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">100%</span>
          <span class="stat-label"><PERSON><PERSON><PERSON> ph<PERSON></span>
        </div>
      </div>
    </div>
  </section>

  <!-- Popular Tags -->
  <section class="tags-section">
    <h2 class="section-title">Tags Phổ Biến</h2>
    <div class="tags-container">
      <span 
        *ngFor="let tag of getPopularTags()" 
        class="tag-item"
      >
        {{ tag }}
      </span>
    </div>
  </section>

  <!-- Featured Novels -->
  <section class="featured-novels" *ngIf="novels.length > 0">
    <div class="section-header">
      <h2 class="section-title">Truyện {{ genre.title }} Nổi Bật</h2>
      <p class="section-description">
        Những bộ truyện {{ genre.title }} được đánh giá cao và yêu thích nhất
      </p>
    </div>
    
    <div class="novels-grid">
      <app-card-v1 
        *ngFor="let novel of novels; trackBy: trackByNovel"
        [novel]="novel"
        class="novel-item"
      ></app-card-v1>
    </div>
  </section>

  <!-- Top Novels Section -->
  <section class="top-novels" *ngIf="topNovels.length > 0">
    <div class="section-header">
      <h2 class="section-title">Top {{ genre.title }} Tuần Này</h2>
    </div>
    
    <div class="novels-grid">
      <app-card-v1 
        *ngFor="let novel of topNovels; trackBy: trackByNovel"
        [novel]="novel"
        class="novel-item"
      ></app-card-v1>
    </div>
  </section>

  <!-- New Novels Section -->
  <section class="new-novels" *ngIf="newNovels.length > 0">
    <div class="section-header">
      <h2 class="section-title">{{ genre.title }} Mới Cập Nhật</h2>
    </div>
    
    <div class="novels-grid">
      <app-card-v1 
        *ngFor="let novel of newNovels; trackBy: trackByNovel"
        [novel]="novel"
        class="novel-item"
      ></app-card-v1>
    </div>
  </section>

  <!-- Completed Novels Section -->
  <section class="completed-novels" *ngIf="completedNovels.length > 0">
    <div class="section-header">
      <h2 class="section-title">{{ genre.title }} Đã Hoàn Thành</h2>
    </div>
    
    <div class="novels-grid">
      <app-card-v1 
        *ngFor="let novel of completedNovels; trackBy: trackByNovel"
        [novel]="novel"
        class="novel-item"
      ></app-card-v1>
    </div>
  </section>

  <!-- FAQ Section -->
  <app-faq 
    [faqs]="getFAQsForGenre()"
    [title]="'Câu Hỏi Về Truyện ' + genre.title"
    [showStructuredData]="true"
    class="genre-faq"
  ></app-faq>

  <!-- CTA Section -->
  <section class="cta-section">
    <div class="cta-content">
      <h2 class="cta-title">Khám Phá Thêm Truyện {{ genre.title }}</h2>
      <p class="cta-description">
        Tham gia cộng đồng độc giả yêu thích {{ genre.title }} tại SayTruyenHot
      </p>
      <div class="cta-actions">
        <a [routerLink]="['/the-loai', genre.slug]" class="btn-primary">
          Xem tất cả {{ genre.title }}
        </a>
        <a routerLink="/tim-kiem" class="btn-secondary">
          Tìm kiếm nâng cao
        </a>
      </div>
    </div>
  </section>
</div>
