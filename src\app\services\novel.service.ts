import { HttpClient } from '@angular/common/http';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Announcement } from '@schemas/Announcement';
import { Chapter } from '@schemas/Chapter';
import { ChapterList } from '@schemas/ChapterList';
import { ChapterPage } from '@schemas/ChapterPage';
import {
  NovelStatus,
  SortType,
  TranslationType,
  UserExpType,
  WordCountRange,
} from '@schemas/enum';
import { Genre } from '@schemas/Genre';
import { Novel } from '@schemas/Novel';
import { NovelList } from '@schemas/NovelList';
import { NovelTopView } from '@schemas/NovelTopView';
import { IServiceResponse } from '@schemas/ResponseType';
import globalConfig from 'globalConfig';
import { Observable, of } from 'rxjs';
import { AccountService } from './account.service';
import { UrlService } from './url.service';

@Injectable({
  providedIn: 'root',
})
export class NovelService {

  constructor(
    private httpclient: HttpClient,
    private auth: AccountService,
    private urlService: UrlService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) { }
  getAds() {
    let req = this.httpclient.get(`${this.urlService.API_URL}/ads?`, {
      transferCache: true,
      params: { expiration: 300 },
    });

    return req;
  }
  getHotNovels(page = 1, step = 30) {
    let req = this.httpclient.get<IServiceResponse<NovelList>>(
      `${this.urlService.API_URL}/hotnovels?${new URLSearchParams({
        page: page.toString(),
        step: step.toString(),
      })}`,
      {
        transferCache: true,
        params: { expiration: 300 },
      }
    );

    return req;
  }
  getNovels(params: {
    page: string;
    step: string;
    genre: string;
    sort: string;
    status: string;
  }) {
    // function getRandomArbitrary(min: number, max: number) {
    //   return Math.floor(Math.random() * (max - min) + min);
    // }
    // params.page = getRandomArbitrary(1, 100).toString();

    let searchParams = new URLSearchParams(params).toString();
    let req = this.httpclient.get<IServiceResponse<NovelList>>(
      `${this.urlService.API_URL}/novels?${searchParams}`,
      {
        transferCache: true,
        params: { expiration: 60 },
      }
    );
    return req
  }

  getNovelCoAuthor(novelId: number) {

    let req = this.httpclient.get<IServiceResponse<NovelList>>(
      `${this.urlService.API_URL}/novel/${novelId}/co-author?size=3`,
      {
        transferCache: true,
        params: { expiration: 60 },
      }
    );
    return req
  }
  getRecommendNovels() {


    return this.httpclient.get<IServiceResponse<Novel[]>>(
      `${this.urlService.API_URL}/novel/recommend`,
      {
        transferCache: true,
        params: { expiration: 600 },
      }
    );
  }
  getNovelById(id: string): Observable<IServiceResponse<Novel>> {


    let req = this.httpclient.get<IServiceResponse<Novel>>(
      `${this.urlService.API_URL}/novel/${id}`,
      {
        transferCache: true,
        params: { expiration: 120 },
      }
    );

    return req
  }
  getNovelsByIds(ids: number[]): Observable<IServiceResponse<Novel>> {


    return this.httpclient.get<IServiceResponse<Novel>>(
      `${this.urlService.API_URL}/novelsbyids?ids=${ids.join(',')}`,
      {
        transferCache: true,
        params: { expiration: 300 },
      }
    );
  }

  getChapterPage(id: number) {

    return this.httpclient.get<IServiceResponse<ChapterPage>>(
      `${this.urlService.API_URL}/novel/chapter/${id}`,
      {
        transferCache: true,
        params: { expiration: 300 },
      }
    );
  }
  getChapterNeighbors(id: number) {

    return this.httpclient.get<IServiceResponse<Chapter[]>>(
      `${this.urlService.API_URL}/novel/chapter/${id}/neighbors`,
      {
        transferCache: true,
        params: { expiration: 300 },
      }
    );
  }

  getChapterContent(chapterId: number) {
    return this.httpclient.get(
      `${this.urlService.API_URL}/chapter-content/${chapterId}`
    );
  }
  getChapters(novelid: number, page: number = 1, step: number = 200) {

    let searchParams = new URLSearchParams({
      page: page.toString(),
      step: step.toString(),
    }).toString();

    return this.httpclient.get<IServiceResponse<ChapterList>>(
      `${this.urlService.API_URL}/novel/${novelid}/chapters?${searchParams}`,
      {
        transferCache: true,
        params: { expiration: 60 },
      }
    );
  }
  getTopNovels() {
    let req = this.httpclient.get<IServiceResponse<NovelTopView>>(
      `${this.urlService.API_URL}/novel/topview?`,
      {
        transferCache: true,
        params: { expiration: 600 },
      }
    );
    return req
  }
  getSearchNovel(key: string) {
    return this.httpclient.get<IServiceResponse<Novel[]>>(
      `${this.urlService.API_URL}/novel/search?keyword=${key}`
    );
  }
  getAdvanceSearchNovel(
    page = 1,
    step = 40,
    sort = SortType.TopAll,
    status = NovelStatus.ALL,
    genres = '',
    nogenres = '',
    translation = TranslationType.ALL,
    wordcount = WordCountRange.ALL,
    keyword = ''
  ) {
    let params = {
      page: page.toString(),
      step: step.toString(),
      sort: sort.toString(),
      status: status.toString(),
      genres: genres,
      nogenres: nogenres,
      translation: translation.toString(),
      wordcount: wordcount.toString(),
      keyword: keyword,
    };

    let searchParams = new URLSearchParams(params).toString();

    let req = this.httpclient.get<IServiceResponse<NovelList>>(
      `${this.urlService.API_URL}/novel/advance?${searchParams}`,
      {
        transferCache: true,
        params: { expiration: 60 },
      }
    );
    return req;
  }

  getSimilarNovel(idnovel: number) {
    let req = this.httpclient.get<IServiceResponse<Novel[]>>(
      `${this.urlService.API_URL}/novel/similar/${idnovel}`,
      {
        transferCache: true,
        params: { expiration: 300 },
      }
    );
    return req;
  }

  getGenres(): Observable<Genre[]> {
    return of(genres);
  }

  getGenreById(id: number) {
    const genre = genres.find((g) => g.id === id);
    return genre ? genre : null;
  }
  updateViewAndExp(
    novelId: number,
    chapterId: number,
    exp: UserExpType.Chapter
  ) {
    let req = this.httpclient.get(
      `${this.urlService.API_URL}/novel/view_exp?novelId=${novelId}&exp=${exp}&chapterId=${chapterId}`
    );
    return req;
  }
  getAnouncement(): Observable<IServiceResponse<Announcement[]>> {


    let req = this.httpclient.get<IServiceResponse<Announcement[]>>(
      `${this.urlService.API_URL}/announcement`,
      {
        transferCache: true,
        params: { expiration: 300 },
      }
    );
    return req;
  }
  onUnFollowClick(idNovel: number) {
    return this.httpclient.get(
      `${this.urlService.API_URL}/user/follow/follow/${idNovel}`
    );
  }

  async getAudioOfChapter(chapter_id: number, voicser: string) {
    if (!this.auth.isAuthenticated()) {
      throw new Error('User is not authenticated');
    }
    // const params = new URLSearchParams({ token: String(token), chapter_id: chapter_id.toString() })
    const params = new URLSearchParams({
      chapter_id: chapter_id.toString(),
      voice: voicser.toString(),
    });
    const url = `${globalConfig.BASE_CHAT_URL}/tts?${params}`;
    let headers = {
      Authorization: `Bearer ${this.auth.getAuthorizationToken()}`,
    };

    const response = await fetch(url, {
      method: 'GET',
      headers: headers,
    });
    if (!response.body) {
      throw new Error('No response body from TTS service');
    }
    return response.body; // Return the response body (stream) to the component

  }
}


const genres = [{
  id: 1,
  title: '1v1',
  slug: '1v1',
  group: 1,
  description:
    'Truyện tình cảm chỉ có một nam chính và một nữ chính, không có tình tay ba hay nhiều mối quan hệ rắc rối.',
},
{
  id: 2,
  title: 'bách hợp',
  slug: 'bach-hop',
  group: 1,
  description:
    'Truyện xoay quanh tình yêu giữa hai nhân vật nữ, thể hiện tình cảm sâu sắc và cảm động.',
},
{
  id: 3,
  title: 'chữa lành',
  slug: 'chua-lanh',
  group: 1,
  description:
    'Truyện mang đến cảm giác nhẹ nhàng, chữa lành tâm hồn với nội dung ấm áp, tích cực.',
},
{
  id: 4,
  title: 'cung đấu',
  slug: 'cung-dau',
  group: 3,
  description:
    'Truyện về các âm mưu, tranh đấu trong cung đình, xoay quanh hậu cung, hoàng hậu, phi tần.',
},
{
  id: 5,
  title: 'cạnh kỹ',
  slug: 'canh-ky',
  group: 5,
  description:
    'Truyện có nội dung về thi đấu, tranh tài giữa các nhân vật ở nhiều lĩnh vực khác nhau.',
},
{
  id: 6,
  title: 'cổ đại',
  slug: 'co-dai',
  group: 3,
  description:
    'Truyện lấy bối cảnh thời cổ đại, có thể dựa trên lịch sử hoặc là thế giới giả tưởng.',
},
{
  id: 7,
  title: 'du hí',
  slug: 'du-hi',
  group: 4,
  description:
    'Truyện liên quan đến trò chơi thực tế ảo, thế giới game hoặc những cuộc phiêu lưu trong game.',
},
{
  id: 8,
  title: 'dã sử',
  slug: 'da-su',
  group: 3,
  description:
    'Truyện lấy cảm hứng từ lịch sử nhưng không hoàn toàn tuân theo sự kiện có thật.',
},
{
  id: 9,
  title: 'dị giới',
  slug: 'di-gioi',
  group: 2,
  description:
    'Truyện về nhân vật chính đến một thế giới khác, có thể là thần thoại, ma pháp hoặc tiên hiệp.',
},
{
  id: 10,
  title: 'dị năng',
  slug: 'di-nang',
  group: 2,
  description:
    'Truyện có nhân vật sở hữu năng lực siêu nhiên như điều khiển lửa, nước, đọc suy nghĩ.',
},
{
  id: 11,
  title: 'fanfic',
  slug: 'fanfic',
  group: 1,
  description:
    'Truyện do fan sáng tác dựa trên các tác phẩm nổi tiếng như anime, phim, tiểu thuyết.',
},
{
  id: 12,
  title: 'gia đình',
  slug: 'gia-dinh',
  group: 5,
  description:
    'Truyện xoay quanh tình cảm gia đình, các mối quan hệ giữa cha mẹ, con cái, anh chị em.',
},
{
  id: 13,
  title: 'gia đấu',
  slug: 'gia-dau',
  group: 3,
  description:
    'Truyện về đấu đá trong gia tộc, âm mưu giữa các thành viên để tranh giành quyền lợi.',
},
{
  id: 14,
  title: 'hiện đại',
  slug: 'hien-dai',
  group: 5,
  description:
    'Truyện lấy bối cảnh thế giới hiện đại với công nghệ, cuộc sống thường nhật.',
},
{
  id: 15,
  title: 'huyền huyễn',
  slug: 'huyen-huyen',
  group: 2,
  description:
    'Truyện có yếu tố huyền bí, tu luyện, thần tiên, yêu ma hoặc pháp thuật kỳ ảo.',
},
{
  id: 16,
  title: 'huyền nghi',
  slug: 'huyen-nghi',
  group: 2,
  description:
    'Truyện có yếu tố bí ẩn, điều tra những vụ án kỳ lạ hoặc khám phá sự thật ẩn giấu.',
},
{
  id: 17,
  title: 'hài hước',
  slug: 'hai-huoc',
  group: 5,
  description:
    'Truyện có nội dung vui nhộn, gây cười với những tình huống hài hước, dí dỏm.',
},
{
  id: 18,
  title: 'hành động',
  slug: 'hanh-dong',
  group: 4,
  description:
    'Truyện có nhiều pha chiến đấu, truy đuổi kịch tính, xoay quanh các nhiệm vụ nguy hiểm.',
},
{
  id: 19,
  title: 'hào môn',
  slug: 'hao-mon',
  group: 3,
  description:
    'Truyện về thế giới thượng lưu, các đại gia tộc quyền thế và mối quan hệ phức tạp.',
},
{
  id: 20,
  title: 'hậu cung',
  slug: 'hau-cung',
  group: 3,
  description:
    'Truyện về cuộc sống trong hậu cung với nhiều phi tần, tranh đấu để giành sự sủng ái.',
},
{
  id: 21,
  title: 'hậu tận thế',
  slug: 'hau-tan-the',
  group: 4,
  description:
    'Truyện lấy bối cảnh thế giới sau thảm họa, nơi nhân loại phải tìm cách sinh tồn.',
},
{
  id: 22,
  title: 'hắc ám',
  slug: 'hac-am',
  group: 4,
  description:
    'Truyện có nội dung u ám, phản diện mạnh, tập trung vào thế giới ngầm hoặc xã hội đen.',
},
{
  id: 23,
  title: 'hệ thống',
  slug: 'he-thong',
  group: 2,
  description:
    'Truyện nhân vật chính có hệ thống hỗ trợ giúp họ thăng cấp, mạnh lên nhanh chóng.',
},
{
  id: 24,
  title: 'khoa huyễn',
  slug: 'khoa-huyen',
  group: 2,
  description:
    'Truyện viễn tưởng với công nghệ tiên tiến, du hành vũ trụ, trí tuệ nhân tạo.',
},
{
  id: 25,
  title: 'khác',
  slug: 'khac',
  group: 5,
  description:
    'Các thể loại không nằm trong danh sách chính, đa dạng phong cách.',
},
{
  id: 26,
  title: 'kinh dị',
  slug: 'kinh-di',
  group: 4,
  description:
    'Truyện gây sợ hãi với yếu tố ma quái, tâm linh, sinh vật kỳ dị hoặc tội phạm bí ẩn.',
},
{
  id: 27,
  title: 'kiếm hiệp',
  slug: 'kiem-hiep',
  group: 4,
  description:
    'Truyện về thế giới giang hồ, cao thủ võ lâm, tranh đoạt bí kíp, ân oán giang hồ.',
},
{
  id: 28,
  title: 'kỳ bí',
  slug: 'ky-bi',
  group: 2,
  description:
    'Truyện có yếu tố kỳ lạ, khó giải thích, thường mang màu sắc thần bí, siêu nhiên.',
},
{
  id: 29,
  title: 'kỳ ảo',
  slug: 'ky-ao',
  group: 2,
  description:
    'Truyện có bối cảnh giả tưởng với phép thuật, sinh vật huyền thoại, thế giới độc đáo.',
},
{
  id: 30,
  title: 'light novel',
  slug: 'light-novel',
  group: 5,
  description:
    'Tiểu thuyết ngắn gọn, phong cách Nhật Bản, thường có hình minh họa đi kèm.',
},
{
  id: 31,
  title: 'linh dị',
  slug: 'linh-di',
  group: 2,
  description:
    'Truyện có yếu tố tâm linh, ma quỷ, thần tiên, linh hồn và thế giới bên kia.',
},
{
  id: 32,
  title: 'lịch sử',
  slug: 'lich-su',
  group: 3,
  description:
    'Truyện tái hiện bối cảnh và nhân vật qua các giai đoạn lịch sử.',
},
{
  id: 33,
  title: 'mạt thế',
  slug: 'mat-the',
  group: 4,
  description:
    'Truyện về tận thế, dịch bệnh, zombie, chiến tranh hủy diệt, nhân loại sụp đổ.',
},
{
  id: 34,
  title: 'mỹ thực',
  slug: 'my-thuc',
  group: 4,
  description: 'Truyện tập trung miêu tả nghệ thuật ẩm thực và món ngon.',
},
{
  id: 35,
  title: 'ngôn tình',
  slug: 'ngon-tinh',
  group: 1,
  description:
    'Truyện tình cảm nam nữ, tập trung vào cảm xúc, mối quan hệ giữa các nhân vật.',
},
{
  id: 36,
  title: 'ngược',
  slug: 'nguoc',
  group: 1,
  description:
    'Truyện có tình tiết đau khổ, bi thương, nhân vật chính chịu nhiều tổn thương.',
},
{
  id: 37,
  title: 'nữ cường',
  slug: 'nu-cuong',
  group: 1,
  description:
    'Truyện có nữ chính mạnh mẽ, độc lập, không phụ thuộc vào nam chính.',
},
{
  id: 38,
  title: 'nữ phụ',
  slug: 'nu-phu',
  group: 1,
  description:
    'Truyện lấy góc nhìn của nhân vật phụ là nữ, thường cạnh tranh với nữ chính.',
},
{
  id: 39,
  title: 'phiêu lưu',
  slug: 'phieu-luu',
  group: 4,
  description:
    'Truyện có hành trình khám phá, chinh phục thử thách, tìm kiếm kho báu hoặc thế giới mới.',
},
{
  id: 40,
  title: 'phương tây',
  slug: 'phuong-tay',
  group: 3,
  description: 'Truyện lấy bối cảnh hoặc văn hóa phương Tây, Âu – Mỹ.',
},
{
  id: 41,
  title: 'phản anh hùng',
  slug: 'phan-anh-hung',
  group: 4,
  description: 'Truyện với nhân vật chính mang nét phản diện, mơ hồ đạo đức.',
},
{
  id: 42,
  title: 'quan trường',
  slug: 'quan-truong',
  group: 3,
  description:
    'Truyện xoay quanh đấu đá quyền lực trong triều đình, quan trường.',
},
{
  id: 43,
  title: 'quyền mưu',
  slug: 'quyen-muu',
  group: 3,
  description: 'Truyện về tranh đoạt quyền lực, âm mưu, toan tính.',
},
{
  id: 44,
  title: 'quân sự',
  slug: 'quan-su',
  group: 3,
  description:
    'Truyện xoay quanh chiến tranh, binh pháp, quân đội, vũ khí hiện đại hoặc cổ trang.',
},
{
  id: 45,
  title: 'sảng văn',
  slug: 'sang-van',
  group: 4,
  description:
    'Truyện có nhân vật chính bá đạo, luôn chiến thắng dễ dàng, đọc thoải mái không căng thẳng.',
},
{
  id: 46,
  title: 'sắc',
  slug: 'sac',
  group: 1,
  description: 'Truyện chứa yếu tố gợi cảm, tình dục rõ nét.',
},
{
  id: 47,
  title: 'sủng',
  slug: 'sung',
  group: 1,
  description: 'Truyện ngọt ngào, nhân vật chính bị chiều chuộng tuyệt đối.',
},
{
  id: 48,
  title: 'thám hiểm',
  slug: 'tham-hiem',
  group: 4,
  description: 'Truyện nhân vật khám phá vùng đất mới, bí ẩn.',
},
{
  id: 49,
  title: 'thăng cấp lưu',
  slug: 'thang-cap-luu',
  group: 2,
  description: 'Truyện nhân vật liên tục tiến cấp, mạnh lên qua thử thách.',
},
{
  id: 50,
  title: 'tiên hiệp',
  slug: 'tien-hiep',
  group: 2,
  description:
    'Truyện về con đường tu luyện tiên đạo, phi thăng thành tiên, chiến đấu với yêu ma.',
},
{
  id: 51,
  title: 'tiểu thuyết',
  slug: 'tieu-thuyet',
  group: 5,
  description: 'Tác phẩm văn học chữ có độ dài và cốt truyện đầy đủ.',
},
{
  id: 52,
  title: 'trinh thám',
  slug: 'trinh-tham',
  group: 4,
  description:
    'Truyện điều tra phá án, khám phá bí ẩn, các vụ án giết người hoặc âm mưu lớn.',
},
{
  id: 53,
  title: 'truyện audio',
  slug: 'truyen-audio',
  group: 5,
  description: 'Truyện được thể hiện dưới dạng thu âm, dành cho tai nghe.',
},
{
  id: 54,
  title: 'truyện teen',
  slug: 'truyen-teen',
  group: 5,
  description: 'Truyện hướng đến đối tượng thanh thiếu niên, tuổi học sinh.',
},
{
  id: 55,
  title: 'trùng sinh',
  slug: 'trung-sinh',
  group: 2,
  description: 'Nhân vật sinh lại sau khi chết, sống trong thân xác mới.',
},
{
  id: 56,
  title: 'trưởng thành',
  slug: 'truong-thanh',
  group: 5,
  description:
    'Truyện khắc họa quá trình phát triển, chín chắn của nhân vật.',
},
{
  id: 57,
  title: 'trọng sinh',
  slug: 'trong-sinh',
  group: 2,
  description:
    'Nhân vật quay về quá khứ với ký ức kiếp trước để thay đổi số mệnh.',
},
{
  id: 58,
  title: 'tây huyễn',
  slug: 'tay-huyen',
  group: 2,
  description: 'Huyền ảo phương Tây, ma thuật và sinh vật thần bí Âu châu.',
},
{
  id: 59,
  title: 'tổng tài',
  slug: 'tong-tai',
  group: 1,
  description: 'Ngôn tình với nam chính là CEO, tổng giám đốc quyền lực.',
},
{
  id: 60,
  title: 'việt nam',
  slug: 'viet-nam',
  group: 5,
  description: 'Truyện lấy bối cảnh hoặc phong cách văn học Việt Nam.',
},
{
  id: 61,
  title: 'võ hiệp',
  slug: 'vo-hiep',
  group: 4,
  description:
    'Truyện về võ thuật, các môn phái giang hồ, cao thủ huyền thoại tranh bá thiên hạ.',
},
{
  id: 62,
  title: 'võng du',
  slug: 'vong-du',
  group: 2,
  description:
    'Truyện liên quan đến game online, nhân vật chính phát triển trong thế giới ảo.',
},
{
  id: 63,
  title: 'vườn trường',
  slug: 'vuon-truong',
  group: 5,
  description:
    'Truyện học đường, chủ yếu diễn ra trong môi trường trường học.',
},
{
  id: 64,
  title: 'xuyên không',
  slug: 'xuyen-khong',
  group: 2,
  description:
    'Truyện nhân vật chính bị đưa đến thời gian hoặc thế giới khác, có thể cổ đại hoặc tương lai.',
},
{
  id: 65,
  title: 'xuyên nhanh',
  slug: 'xuyen-nhanh',
  group: 2,
  description: 'Xuyên không thời gian ngay lập tức, ít gián đoạn chuyển đổi.',
},
{
  id: 66,
  title: 'y thuật',
  slug: 'y-thuat',
  group: 2,
  description: 'Truyện liên quan y học, chữa bệnh và kỹ năng y thuật.',
},
{
  id: 67,
  title: 'đam mỹ',
  slug: 'dam-my',
  group: 1,
  description:
    'Truyện tình cảm giữa hai nhân vật nam, thể hiện tình yêu và cảm xúc chân thành.',
},
{
  id: 68,
  title: 'điền văn',
  slug: 'dien-van',
  group: 1,
  description: 'Truyện nông thôn, đồng quê, cuộc sống miền yên bình.',
},
{
  id: 69,
  title: 'đoản văn',
  slug: 'doan-van',
  group: 1,
  description: 'Truyện ngắn, thường chỉ vài chương, nội dung cô đọng.',
},
{
  id: 70,
  title: 'đô thị',
  slug: 'do-thi',
  group: 5,
  description:
    'Truyện lấy bối cảnh thành phố, cuộc sống hiện đại, xoay quanh kinh doanh, xã hội.',
},
{
  id: 71,
  title: 'đông phương',
  slug: 'dong-phuong',
  group: 3,
  description: 'Truyện lấy bối cảnh Trung Quốc hoặc văn hóa Á Đông.',
},
{
  id: 72,
  title: 'đồng nhân',
  slug: 'dong-nhan',
  group: 1,
  description:
    'Fanfic khai thác thế giới và nhân vật có sẵn từ tác phẩm gốc.',
}
];
