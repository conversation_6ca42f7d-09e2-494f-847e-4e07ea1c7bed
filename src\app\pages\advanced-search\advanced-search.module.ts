import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { BreadcrumbComponent } from '@components/breadcrumb/breadcrumb.component';
import { NovelCardV1Component } from '@components/novel-card/card-v1/card-v1.component';
import { NovelCardV2Component } from '@components/novel-card/card-v2/card-v2.component';
import { PaginationComponent } from '@components/pagination/pagination.component';
import { AdvancedSearchComponent } from './advanced-search.component';
import { FilterComponent } from './filter/filter.component';
import { SpinnerComponent } from '@components/spinner/spinner.component';
import { EmptyComponent } from '@components/empty/empty.component';
import { SelectComponent } from '@components/select/select.component';
import { GenresComponent } from '@components/genres/genres.component';
import { ClickOutsideDirective } from '@directives/click-outside.directive';

const routes: Routes = [
  {
    path: '', 
    component: AdvancedSearchComponent
  },
  {
    path: ':page',
    component: AdvancedSearchComponent
  }
];

@NgModule({
  declarations: [FilterComponent, AdvancedSearchComponent,
  ],
  imports: [RouterModule.forChild(routes),
    CommonModule,
    PaginationComponent,
    FormsModule,
    BreadcrumbComponent,
    NovelCardV1Component,
    NovelCardV2Component,
    SpinnerComponent,
    EmptyComponent,
    SelectComponent,
    GenresComponent,
    ClickOutsideDirective
  ]
})
export class AdvancedSearchModule { }
