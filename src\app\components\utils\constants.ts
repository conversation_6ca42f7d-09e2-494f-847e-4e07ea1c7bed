import { IOption } from "@schemas/IOption";
import { NovelStatus, SortType, TranslationType, WordCountRange } from "../../schemas";

export interface IFilter extends IOption {
  selected?: boolean;
  select?: number;
}

export interface IFilters {
  status: IFilter[];
  sorts: IFilter[];
  translations: IFilter[];
  wordcounts: IFilter[];
  genres: IFilter[];
}
export interface IFilterOptions {
  sort: any;
  status: any;
  translation: any;
  wordcount: any;
  keyword: any;
  genres: any;
  nogenres: any;
}
export const advancedFiltersOptions: IFilters = {
  status: [
    {
      label: 'Tất Cả',
      value: NovelStatus.ALL,
    },
    {
      label: 'Đang tiến hành',
      value: NovelStatus.ONGOING,
    },
    {
      label: 'Hoàn thành',
      value: NovelStatus.COMPLETED,
    },
  ],

  sorts: [
    {
      label: 'Mới cập nhật',
      value: SortType.LastUpdate,
    },
    {
      label: 'Top All',
      value: SortType.TopAll,
    },
    {
      label: 'Top ngày',
      value: SortType.TopDay,
    },
    {
      label: 'Top tuần',
      value: SortType.TopWeek,
    },
    {
      label: 'Top tháng',
      value: SortType.TopMonth,
    },
    {
      label: 'Số chương',
      value: SortType.Chapter,
    },
    {
      label: 'Theo dõi',
      value: SortType.TopFollow,
    },
    {
      label: 'Bình luận',
      value: SortType.TopComment,
    },
    {
      label: 'Truyện mới',
      value: SortType.NewNovel,
    },
  ],

  translations: [
    {
      label: 'Tất cả',
      value: TranslationType.ALL,
    },
    {
      label: 'Máy dịch',
      value: TranslationType.Machine,
    },
    {
      label: 'Người dịch',
      value: TranslationType.Human,
    }

  ],
  wordcounts: [
    {
      label: 'Tất cả',
      value: WordCountRange.ALL,

    },
    {
      label: 'Dưới 100k',
      value: WordCountRange.Under100k,
    },
    {
      label: '100k - 500k',
      value: WordCountRange.Between100KAnd500K,
    },
    {
      label: 'Trên 500k',
      value: WordCountRange.Over500k,
    }

  ],
  genres: []
};

// export const rankFiltersOptions: IFilters = {
//   status: [
//     {
//       label: 'Tất Cả',
//       value: NovelStatus.ALL,
//     },
//     {
//       label: 'Đang Ra',
//       value: NovelStatus.ONGOING,
//     },
//     {
//       label: 'Hoàn Thành',
//       value: NovelStatus.COMPLETED,
//     },
//   ],

//   sorts: [
//     {
//       label: 'Top All',
//       value: SortType.TopAll,
//     },
//     {
//       label: 'Top ngày',
//       value: SortType.TopDay,
//     },
//     {
//       label: 'Top tuần',
//       value: SortType.TopWeek,
//     },
//     {
//       label: 'Top tháng',
//       value: SortType.TopMonth,
//     },

//     {
//       label: 'Chapter',
//       value: SortType.Chapter,
//     },

//     {
//       label: 'Truyện mới',
//       value: SortType.NewNovel,
//     },
//     {
//       label: 'Ngày cập nhật',
//       value: SortType.LastUpdate,
//     },
//   ],
//   translations: undefined,
//   wordcounts: undefined
// };



const BITRATE = 64000
export const BYTES_PER_SECOND = BITRATE / 8

export const VOICE = {
  GG: "gtts",
  MALEEGE: "vi-VN-NamMinhNeural",
  FEMALEege: "vi-VN-HoaiMyNeural"
}
