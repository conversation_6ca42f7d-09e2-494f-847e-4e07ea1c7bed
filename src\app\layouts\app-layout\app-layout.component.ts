import { isPlatformServer } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, Inject, PLATFORM_ID, ViewChild, ViewContainerRef, ViewEncapsulation } from '@angular/core';
import { RouterModule } from '@angular/router';
import { NovelDetailPopupComponent } from '@components/novel-info-popup/novel-info.component';
import { SettingType } from '@schemas/SettingType.enum';
import { DynamicLoadingService } from '@services/dynamic.loading.service';
import { EventService } from '@services/event.service';
import { SettingService } from '@services/setting.service';
import { FooterComponent } from 'src/app/shared/footer/footer.component';
import { NavbarComponent } from 'src/app/shared/navbar/navbar.component';

@Component({
  selector: 'app-layout',
  standalone: true,
  templateUrl: './app-layout.component.html',
  styleUrl: './app-layout.component.scss',
  imports: [RouterModule, NavbarComponent, FooterComponent, NovelDetailPopupComponent], 
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LayoutComponent {
scrollToTop() {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
}
  themValue = "";

  @ViewChild('layout') layoutRef?: ElementRef<HTMLElement>;
  @ViewChild('dynamicContainer', { read: ViewContainerRef }) dynamicContainer?: ViewContainerRef;
  constructor(public settingService: SettingService,
    public eventService: EventService,
    private dynamicLoadingService: DynamicLoadingService,
    @Inject(PLATFORM_ID) private platformId: Object

  ) {

  }
  ngOnInit() {
    if (isPlatformServer(this.platformId)) return;

    this.settingService.OnSettingChange(SettingType.Theme).subscribe(themeSetting => {
      this.themValue = themeSetting.value
    }
    );
    this.settingService.OnSettingChange(SettingType.BgColor).subscribe(bgSetting => {
      this.changeBgColor(bgSetting.options?.find(o => o.value == bgSetting.value).colors)

    })
    this.settingService.OnSettingChange(SettingType.PrimaryColor).subscribe(primarySetting => {
      this.changePrimaryColor(primarySetting.options?.find(o => o.value == primarySetting.value).colors)
    }
    );
  } 
  ngAfterViewInit() {
    if (isPlatformServer(this.platformId)) return;
    this.dynamicLoadingService.viewContainerRef = this.dynamicContainer;
  }

  changePrimaryColor(colors: { '100': string, '200': string, '300': string }) {
    document.body.style.setProperty('--color-primary-100', colors['100']);
    document.body.style.setProperty('--color-primary-200', colors['200']);
    document.body.style.setProperty('--color-primary-300', colors['300']);
  }
  changeBgColor(colors: { '100': string, '200': string, '300': string }) {
    document.body.style.setProperty('--color-background-100', colors['100']);
    document.body.style.setProperty('--color-background-200', colors['200']);
    document.body.style.setProperty('--color-background-300', colors['300']);
  }

}
