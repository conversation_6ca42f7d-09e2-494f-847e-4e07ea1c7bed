import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';
import { RouterLink } from '@angular/router';
@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrl: './breadcrumb.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule,RouterLink],
  encapsulation: ViewEncapsulation.None,
  
})
export class BreadcrumbComponent {
  @Input() Links: { label: string | undefined; url: string | undefined }[] = [];

}
