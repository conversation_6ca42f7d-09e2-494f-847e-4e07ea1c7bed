<div
  (appClickOutside)="closeDropdown()"
  class="relative inline-flex w-full items-center"
  [ngClass]="size"
>
  <button class="w-full" (click)="toggleDropdown()">
    <ng-container [ngTemplateOutlet]="customBtn || selectButton" />
  </button>

  <div
    [@fadeInOut]
    *ngIf="isDropdownOpen"
    class="dropdown scrollbar-style-lg"
    [ngClass]="{
      'top-full mt-0.5 origin-top': direct === 'down',
      'bottom-full mb-0.5 origin-bottom': direct === 'up',
      'w-max': !isFit,
      'w-full': isFit,
    }"
  >
    <div
      *ngFor="let option of options; let i = index"
      class="dropdown-item"
      (click)="selectOption(option)"
      [ngClass]="{ 
        'bg-primary-50 text-white hover:bg-primary-200': i === selectedIdx,
      
      }"
    >
      <span *ngIf="checkmark && i === selectedIdx">
        <svg
          class="size-5 inline-block text-green-400"
          viewBox="0 0 512 512"
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
        >
          <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
          <g
            id="SVGRepo_tracerCarrier"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></g>
          <g id="SVGRepo_iconCarrier">
            <path
              fill="currentColor"
              d="M17.47 250.9C88.82 328.1 158 397.6 224.5 485.5c72.3-143.8 146.3-288.1 268.4-444.37L460 26.06C356.9 135.4 276.8 238.9 207.2 361.9c-48.4-43.6-126.62-105.3-174.38-137z"
            ></path>
          </g>
        </svg>
      </span>
      <span class="line-clamp-1">
        {{ option.label }}
      </span>
    </div>
  </div>
</div>

<ng-template #selectButton>
  <span
    class="select-padding flex-center w-full text-left bg-white dark:bg-dark-600 border border-gray-300 dark:border-neutral-600 rounded-md shadow-sm focus:border-primary-100"
    title="Select an option"
  >
    <span>
      {{
        this.selectedIdx >= 0 ? options[this.selectedIdx].label : placeholder
      }}
    </span>

    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="h-5 w-5 inline-block ml-auto text-gray-400 dark:text-neutral-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    >
      <path d="M6 9l6 6 6-6"></path>
    </svg>
  </span>
</ng-template>

<ng-content />
