import { Component } from '@angular/core';
import { SeoService } from '@services/seo.service';
import globalConfig from 'globalConfig';

@Component({
  selector: 'app-about-us',
  imports: [],
  templateUrl: './about-us.component.html',
  styleUrl: './about-us.component.scss'
})
export class AboutUsComponent {
  appName = globalConfig.APP_NAME;
  
  constructor(private seoService: SeoService) {
    this.setupSeo();
  }

  private setupSeo(): void {
    const title = `Giới thiệu về ${this.appName}`;
    const description = `Tìm hiểu về ${this.appName} - Trang web đọc truyện chữ online miễn phí hàng đầu Việt Nam. Kho tàng truyện chữ phong phú với nhiều thể loại đa dạng, chất lượng cao và cập nhật liên tục.`;
    const url = `${globalConfig.BASE_URL}/ve-chung-toi`;

    const seoData = {
      title,
      description,
      type: 'website' as const,
      image: `${globalConfig.BASE_URL}/logo.png`,
      url,
      siteName: this.appName,
      locale: 'vi_VN',
      twitterCard: 'summary' as const,
      keywords: `giới thiệu ${this.appName}, về chúng tôi, website đọc truyện, truyện chữ online, ${this.appName}, đọc truyện miễn phí, kho truyện chữ`,
      canonical: url,
      noindex: false,
      nofollow: false
    };

    this.seoService.setSEOData(seoData);

    // Add structured data schemas
    const schemas = [
      this.generateAboutPageSchema(),
      this.seoService.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: '/' },
        { name: 'Giới thiệu', url: '/ve-chung-toi' }
      ])
    ];

    this.seoService.addStructuredData(schemas);
  }

  private generateAboutPageSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'AboutPage',
      'name': `Giới thiệu về ${this.appName}`,
      'description': `Thông tin chi tiết về ${this.appName} - Website đọc truyện chữ online hàng đầu Việt Nam`,
      'url': `${globalConfig.BASE_URL}/ve-chung-toi`,
      'mainEntity': {
        '@type': 'Organization',
        'name': this.appName,
        'url': globalConfig.BASE_URL,
        'description': 'Website đọc truyện chữ online miễn phí với kho tàng truyện chữ phong phú',
        'foundingDate': '2025',
        'serviceType': 'Online Novel Reading Platform'
      },
      'breadcrumb': {
        '@type': 'BreadcrumbList',
        'itemListElement': [
          {
            '@type': 'ListItem',
            'position': 1,
            'name': 'Trang chủ',
            'item': globalConfig.BASE_URL
          },
          {
            '@type': 'ListItem',
            'position': 2,
            'name': 'Giới thiệu',
            'item': `${globalConfig.BASE_URL}/ve-chung-toi`
          }
        ]
      }
    };
  }
}
