<div *ngIf="visible" [@fadeInOut] class="chapter-popup-panel origin-top">

  <svg class="size-8 " viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round"
    stroke-linejoin="round">
    <path stroke="none" d="M0 0h24v24H0z" />
    <path d="M16 6h3a1 1 0 0 1 1 1v11a2 2 0 0 1 -4 0v-13a1 1 0 0 0 -1 -1h-10a1 1 0 0 0 -1 1v12a3 3 0 0 0 3 3h11" />
    <line x1="8" y1="8" x2="12" y2="8" />
    <line x1="8" y1="12" x2="12" y2="12" />
    <line x1="8" y1="16" x2="12" y2="16" />
  </svg>

  <h2 class="text-lg font-semibold mb-4 ">Danh sách ch<PERSON>ng</h2>
  <div class="w-full bg-white dark:bg-dark-650 overflow-hidden">
    <input type="text" maxlength="255"
      class="w-full px-3 py-2 outline-none bg-neutral-100 dark:bg-dark-500 rounded-xl dark:text-white"
      placeholder="Tìm chương..." (input)="filterChapters(searchQuery.value)" #searchQuery />
      <div class="h-64 w-full flex mt-4">
        <app-loop-scroll *ngIf="!isLoading" class="flex h-full w-full" 
        [allitems]="filteredChapters" 
        [selectedID]="chapterPage?.id"
        [itemHeight]="36"
        >
          <ng-template #ItemTemplate let-item="item">
            <a 
              (click)="close()"
              [routerLink]="['/',chapterPage?.novel?.url, 'chuong-'+item?.slug, item.id]"
              class="my-0.5 line-clamp-1 rounded-sm px-4 py-1 cursor-pointer dark:text-white hover:bg-neutral-200 hover:dark:bg-dark-600"
              [ngClass]="{
                'bg-primary-50 text-white dark:bg-dark-600': item.id === chapterPage?.id
              }">
              Chương {{item.slug}}: {{ item.title }}
            </a>
          </ng-template>
        </app-loop-scroll>
      
      </div>
  </div>
</div>