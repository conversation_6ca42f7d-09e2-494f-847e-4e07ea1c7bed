import { CommonModule } from '@angular/common';
import { Component, OnInit, ChangeDetectionStrategy, ViewEncapsulation } from '@angular/core';
import { RouterLink } from '@angular/router';
import { SeoService } from '@services/seo.service';
import { NovelService } from '@services/novel.service';
import { FaqComponent, FAQItem } from '@components/faq/faq.component';
import { BreadcrumbComponent } from '@components/breadcrumb/breadcrumb.component';
import { Novel } from '@schemas/Novel';
import globalConfig from 'globalConfig';
import { NovelCardV1Component } from '@components/novel-card/card-v1/card-v1.component';

@Component({
  selector: 'app-truyen-chu-online',
  templateUrl: './truyen-chu-online.component.html',
  styleUrls: ['./truyen-chu-online.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule, 
    RouterLink, 
    FaqComponent, 
    NovelCardV1Component, 
    BreadcrumbComponent
  ]
})
export class TruyenChuOnlineComponent implements OnInit {
  featuredNovels: Novel[] = [];
  popularGenres = [
    { name: 'Ngôn Tình', slug: 'ngon-tinh', description: 'Truyện tình cảm lãng mạn, ngọt ngào' },
    { name: 'Tiên Hiệp', slug: 'tien-hiep', description: 'Tu tiên, tu ma, thế giới huyền bí' },
    { name: 'Kiếm Hiệp', slug: 'kiem-hiep', description: 'Giang hồ hiệp khách, võ lâm tranh đấu' },
    { name: 'Đô Thị', slug: 'do-thi', description: 'Cuộc sống hiện đại, tình yêu đô thị' },
    { name: 'Huyền Huyễn', slug: 'huyen-huyen', description: 'Thế giới kỳ ảo, phép thuật ma pháp' },
    { name: 'Xuyên Không', slug: 'xuyen-khong', description: 'Du hành thời gian, xuyên việt cổ đại' }
  ];

  faqs: FAQItem[] = [
    {
      question: 'Đọc truyện chữ online ở đâu miễn phí và chất lượng nhất?',
      answer: `<strong>SayTruyenHot</strong> là nền tảng đọc truyện chữ online hàng đầu Việt Nam với hơn <strong>50,000+ bộ truyện</strong> miễn phí. Chúng tôi cung cấp trải nghiệm đọc mượt mà, không quảng cáo làm phiền, cập nhật nhanh nhất và hỗ trợ đọc trên mọi thiết bị.`,
      category: 'general',
      keywords: ['đọc truyện miễn phí', 'truyện chữ online', 'saytruyenhot']
    },
    {
      question: 'Có những thể loại truyện chữ nào phổ biến nhất?',
      answer: `Các thể loại truyện chữ phổ biến nhất hiện nay bao gồm: <strong>Ngôn tình</strong> (tình cảm lãng mạn), <strong>Tiên hiệp</strong> (tu tiên ma đạo), <strong>Kiếm hiệp</strong> (võ lâm giang hồ), <strong>Đô thị</strong> (hiện đại), <strong>Huyền huyễn</strong> (thế giới kỳ ảo), và <strong>Xuyên không</strong> (du hành thời gian).`,
      category: 'genres',
      keywords: ['thể loại truyện', 'ngôn tình', 'tiên hiệp', 'kiếm hiệp']
    },
    {
      question: 'Làm sao để theo dõi truyện yêu thích và nhận thông báo cập nhật?',
      answer: `Bạn có thể <strong>đăng ký tài khoản miễn phí</strong> tại SayTruyenHot để sử dụng tính năng <strong>Tủ sách cá nhân</strong>. Khi theo dõi truyện, bạn sẽ nhận được thông báo ngay khi có chương mới, giúp không bỏ lỡ bất kỳ cập nhật nào.`,
      category: 'features',
      keywords: ['theo dõi truyện', 'tủ sách', 'thông báo cập nhật']
    },
    {
      question: 'Truyện chữ online có an toàn cho trẻ em không?',
      answer: `SayTruyenHot có <strong>hệ thống phân loại độ tuổi</strong> rõ ràng. Chúng tôi khuyến khích phụ huynh kiểm tra nội dung trước khi cho trẻ đọc. Các truyện có nội dung không phù hợp sẽ được gắn nhãn cảnh báo rõ ràng.`,
      category: 'safety',
      keywords: ['an toàn trẻ em', 'phân loại độ tuổi', 'kiểm duyệt nội dung']
    },
    {
      question: 'Có thể đọc truyện offline không?',
      answer: `Hiện tại SayTruyenHot hỗ trợ <strong>đọc online</strong> để đảm bảo nội dung luôn được cập nhật mới nhất. Chúng tôi đang phát triển tính năng <strong>tải về đọc offline</strong> cho thành viên VIP trong tương lai gần.`,
      category: 'features',
      keywords: ['đọc offline', 'tải truyện', 'VIP features']
    }
  ];

  breadcrumbLinks = [
    { label: 'Trang chủ', url: '/' },
    { label: 'Đọc Truyện Chữ Online', url: '/truyen-chu-online' }
  ];

  constructor(
    private seoService: SeoService,
    private novelService: NovelService
  ) {}

  ngOnInit() {
    this.setupSEO();
    this.loadFeaturedNovels();
  }

  private setupSEO() {
    const title = 'Đọc Truyện Chữ Online Miễn Phí - Kho Truyện Hay Nhất 2025';
    const description = 'Đọc truyện chữ online miễn phí với hơn 50,000+ bộ truyện hay nhất: ngôn tình, tiên hiệp, kiếm hiệp, đô thị, huyền huyễn. Cập nhật 24/7, không quảng cáo, đọc mượt mà trên mọi thiết bị.';
    const keywords = 'đọc truyện chữ online, truyện chữ miễn phí, truyện hay 2025, ngôn tình online, tiên hiệp hay, kiếm hiệp full, đô thị hot, huyền huyễn mới, xuyên không hay, kho truyện lớn, cập nhật hàng ngày, đọc truyện không quảng cáo';

    const seoData = {
      title,
      description,
      keywords,
      type: 'website' as const,
      image: `${globalConfig.BASE_URL}/logo.png`,
      url: `${globalConfig.BASE_URL}/truyen-chu-online`,
      canonical: `${globalConfig.BASE_URL}/truyen-chu-online`,
      siteName: globalConfig.APP_NAME,
      locale: 'vi_VN',
      twitterCard: 'summary_large_image' as const,
      author: globalConfig.APP_NAME,
      noindex: false,
      nofollow: false
    };

    this.seoService.setSEOData(seoData);

    // Add structured data
    const schemas = [
      this.generateLandingPageSchema(),
      this.generateBreadcrumbSchema(),
      this.generateWebsiteSchema()
    ];

    this.seoService.addStructuredData(schemas);
  }

  private loadFeaturedNovels() {
    this.novelService.getRecommendNovels().subscribe(response => {
      if (response.data) {
        this.featuredNovels = response.data.slice(0, 12);
      }
    });
  }

  private generateLandingPageSchema() {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebPage',
      'name': 'Đọc Truyện Chữ Online Miễn Phí',
      'description': 'Kho truyện chữ online lớn nhất Việt Nam với hơn 50,000+ bộ truyện hay',
      'url': `${globalConfig.BASE_URL}/truyen-chu-online`,
      'mainEntity': {
        '@type': 'ItemList',
        'name': 'Thể loại truyện phổ biến',
        'itemListElement': this.popularGenres.map((genre, index) => ({
          '@type': 'ListItem',
          'position': index + 1,
          'item': {
            '@type': 'Thing',
            'name': genre.name,
            'description': genre.description,
            'url': `${globalConfig.BASE_URL}/the-loai/${genre.slug}`
          }
        }))
      }
    };
  }

  private generateBreadcrumbSchema() {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': this.breadcrumbLinks.map((link, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'name': link.label,
        'item': `${globalConfig.BASE_URL}${link.url}`
      }))
    };
  }

  private generateWebsiteSchema() {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      'name': globalConfig.APP_NAME,
      'url': globalConfig.BASE_URL,
      'potentialAction': {
        '@type': 'SearchAction',
        'target': `${globalConfig.BASE_URL}/tim-kiem?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      }
    };
  }

  trackByGenre(index: number, genre: any): string {
    return genre.slug;
  }

  trackByNovel(index: number, novel: Novel): number {
    return novel.id;
  }
}
