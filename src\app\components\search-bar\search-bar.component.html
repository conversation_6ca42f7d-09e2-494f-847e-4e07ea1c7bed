<button
  type="button"
  title="Tìm kiếm"
  class="btn-search"
  (click)="OnSearchClick()"
>
  <svg
    class="search-icon"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
    />
  </svg>
</button>

<div
  #SearchFrame
  [ngClass]="{ 'z-50': isSearching }"
  class="hidden absolute top-0 right-0 left-0 h-16 md:relative md:flex justify-end md:h-8 md:w-64"
>
  <div class="relative w-full rounded-lg top-2 md:top-0">
    <input
      [(ngModel)]="searchText"
      #SearchInput
      (focus)="OnSearchFocus(true)"
      (focusout)="OnSearchFocus(false)"
      (keydown.enter)="OnSearchEnter()"
      type="text"
      class="custom-transition search-input rounded-lg focus:ring-2 focus:ring-primary-100 bg-white/80 dark:bg-neutral-800/40 backdrop:blur-sm placeholder:text-gray-600 dark:placeholder:text-gray-500"
      placeholder="Tìm truyện..."
    />

    <svg
      (click)="OnSearchEnter()"
      class="h-4 w-4 text-current absolute right-3 top-4 transform -translate-y-1/2 transition-opacity duration-300 z-50 cursor-pointer"
      fill="none"
      viewBox="0 0 24 24"
      stroke="currentColor"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
      />
    </svg>
  </div>

  <!-- <div
    (click)="OnSearchFocus(false)"
    class="fixed -z-10 top-0 bottom-0 right-0 left-0 bg-gray-800 opacity-30"
  ></div> -->
</div>
