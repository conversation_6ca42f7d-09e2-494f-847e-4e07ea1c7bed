import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CarouselComponent } from '@components/carousel/carousel.component';
import { HistoryPanelComponent } from '@components/history-panel/history-panel.component';
import { AuthorIconComponent } from '@components/icons/author/author.component';
import { NovelCardV1Component } from '@components/novel-card/card-v1/card-v1.component';
import { NovelCardV2Component } from '@components/novel-card/card-v2/card-v2.component';
import { SelectComponent } from '@components/select/select.component';
import { SwipperFadeComponent } from '@components/swipper-fade/swipper-fade.component';
import { TopNovelComponent } from '@components/top-novel/top-novel.component';
import { TopdayComponent } from '@components/topday/topday.component';
import { UpdatePanelComponent } from '@components/update-panel/update-panel.component';
import { DateAgoPipe } from 'src/app/shared/pines/date-ago.pine';
import { NumeralPipe } from 'src/app/shared/pines/numeral.pipe';
import { PinesModule } from 'src/app/shared/pines/pines.module';
import { HomeComponent } from './home.component';

const routes: Routes = [{ path: '', component: HomeComponent }];

@NgModule({
  declarations: [HomeComponent],
  imports: [RouterModule.forChild(routes),
    CommonModule,
    CarouselComponent,
    TopdayComponent,
    HistoryPanelComponent,
    NovelCardV1Component,
    NovelCardV2Component,
    PinesModule,
    NumeralPipe,
    DateAgoPipe,
    SelectComponent,
    AuthorIconComponent,
    UpdatePanelComponent,
    SwipperFadeComponent,
    TopNovelComponent
  ],
})
export class HomeModule { 
  constructor() {
  }
}
