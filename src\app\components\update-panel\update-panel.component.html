<div class="flex justify-between my-2">
  <span class="inline-flex items-center gap-2">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="size-6"
      viewBox="0 0 15 15"
      fill="currentColor"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M1.90321 7.29677C1.90321 10.341 4.11041 12.4147 6.58893 12.8439C6.87255 12.893 7.06266 13.1627 7.01355 13.4464C6.96444 13.73 6.69471 13.9201 6.41109 13.871C3.49942 13.3668 0.86084 10.9127 0.86084 7.29677C0.860839 5.76009 1.55996 4.55245 2.37639 3.63377C2.96124 2.97568 3.63034 2.44135 4.16846 2.03202L2.53205 2.03202C2.25591 2.03202 2.03205 1.80816 2.03205 1.53202C2.03205 1.25588 2.25591 1.03202 2.53205 1.03202L5.53205 1.03202C5.80819 1.03202 6.03205 1.25588 6.03205 1.53202L6.03205 4.53202C6.03205 4.80816 5.80819 5.03202 5.53205 5.03202C5.25591 5.03202 5.03205 4.80816 5.03205 4.53202L5.03205 2.68645L5.03054 2.68759L5.03045 2.68766L5.03044 2.68767L5.03043 2.68767C4.45896 3.11868 3.76059 3.64538 3.15554 4.3262C2.44102 5.13021 1.90321 6.10154 1.90321 7.29677ZM13.0109 7.70321C13.0109 4.69115 10.8505 2.6296 8.40384 2.17029C8.12093 2.11718 7.93465 1.84479 7.98776 1.56188C8.04087 1.27898 8.31326 1.0927 8.59616 1.14581C11.4704 1.68541 14.0532 4.12605 14.0532 7.70321C14.0532 9.23988 13.3541 10.4475 12.5377 11.3662C11.9528 12.0243 11.2837 12.5586 10.7456 12.968L12.3821 12.968C12.6582 12.968 12.8821 13.1918 12.8821 13.468C12.8821 13.7441 12.6582 13.968 12.3821 13.968L9.38205 13.968C9.10591 13.968 8.88205 13.7441 8.88205 13.468L8.88205 10.468C8.88205 10.1918 9.10591 9.96796 9.38205 9.96796C9.65819 9.96796 9.88205 10.1918 9.88205 10.468L9.88205 12.3135L9.88362 12.3123C10.4551 11.8813 11.1535 11.3546 11.7585 10.6738C12.4731 9.86976 13.0109 8.89844 13.0109 7.70321Z"
        fill="currentColor"
      />
    </svg>
    <h2 class="text-xl font-semibold">MỚI CẬP NHẬT</h2>
  </span>

  <button class="px-4" aria-label="Xem tất cả">
    <svg
      class="size-5"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
      <g
        id="SVGRepo_tracerCarrier"
        stroke-linecap="round"
        stroke-linejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        <path
          d="M2 12.0701H22"
          stroke="#000000"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        ></path>
        <path
          d="M16 5L21.16 10C21.4324 10.2571 21.6494 10.567 21.7977 10.9109C21.946 11.2548 22.0226 11.6255 22.0226 12C22.0226 12.3745 21.946 12.7452 21.7977 13.0891C21.6494 13.433 21.4324 13.7429 21.16 14L16 19"
          stroke="#000000"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        ></path>
      </g>
    </svg>
  </button>
  <!-- <app-select [selectedValue]="'tat-ca'" [options]="this.options" [size]="'small'" class="w-32"></app-select> -->
</div>
<div class="">
  <ng-container
    *ngFor="let novel of lastestNovels | slice : 0 : 20; let i = index"
  >
    <div
      class="p-1.5 flex-end border-t-[1px] border-dashed dark:border-dark-500"
      [ngClass]="{ 'bg-neutral-400/10': i % 2 == 0 }"
    >
      <a
        [routerLink]="['/truyen', novel.url + '-' + novel.id]"
        class="text-sm font-light"
      >
        <span class="flex-start gap-1">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            class="size-3 flex-shrink-0"
            viewBox="-4.5 0 20 20"
            version="1.1"
            fill="currentColor"
          >
            <g id="SVGRepo_bgCarrier" stroke-width="0" />

            <g
              id="SVGRepo_tracerCarrier"
              stroke-linecap="round"
              stroke-linejoin="round"
            />

            <g id="SVGRepo_iconCarrier">
              <title>arrow_right [#336]</title>
              <desc>Created with Sketch.</desc>
              <defs></defs>
              <g
                id="Page-1"
                stroke="none"
                stroke-width="1"
                fill="currentColor"
                fill-rule="evenodd"
              >
                <g
                  id="Dribbble-Light-Preview"
                  transform="translate(-305.000000, -6679.000000)"
                  fill="currentColor"
                >
                  <g id="icons" transform="translate(56.000000, 160.000000)">
                    <path
                      d="M249.365851,6538.70769 L249.365851,6538.70769 C249.770764,6539.09744 250.426289,6539.09744 250.830166,6538.70769 L259.393407,6530.44413 C260.202198,6529.66364 260.202198,6528.39747 259.393407,6527.61699 L250.768031,6519.29246 C250.367261,6518.90671 249.720021,6518.90172 249.314072,6519.28247 L249.314072,6519.28247 C248.899839,6519.67121 248.894661,6520.31179 249.302681,6520.70653 L257.196934,6528.32352 C257.601847,6528.71426 257.601847,6529.34685 257.196934,6529.73759 L249.365851,6537.29462 C248.960938,6537.68437 248.960938,6538.31795 249.365851,6538.70769"
                      id="arrow_right-[#336]"
                    ></path>
                  </g>
                </g>
              </g>
            </g>
          </svg>
          <span
            (mouseenter)="onHoverNovel(novel)"
            (mouseleave)="onHoverNovel(undefined)"
            class="line-clamp-2 font-normal"
          >
            {{ novel.title }}
          </span>
        </span>
      </a>
      <span class="flex-1"></span>
      <a
        [routerLink]="['/tim-kiem']"
        [queryParams]="{ genres: novel.genres[0].id }"
        class="sm:w-24 text-sm"
      >
        <span class="genre-tag !px-1 !border-0 sm:!border !text-nowrap">{{
          novel.genres[0].title
        }}</span>
      </a>
      <a
        [routerLink]="['/truyen', novel.url + '-' + novel.id]"
        class="text-neutral-500 sm:w-20 text-sm text-nowrap"
        >chương {{novel.numChapter}}</a
      >
      <span
        class="w-16 text-neutral-500 text-end text-sm line-clamp-1 text-nowrap"
        >{{ novel.updateAt | dateAgo }}</span
      >
    </div>
  </ng-container>
</div>
