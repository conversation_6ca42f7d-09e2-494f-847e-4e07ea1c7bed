import { Injectable } from '@angular/core';
import { SeoService } from './seo.service';
import { ContentFreshnessService } from './content-freshness.service';
import { Novel } from '@schemas/Novel';
import { Genre } from '@schemas/Genre';
import { Chapter } from '@schemas/Chapter';
import { FAQItem } from '@components/faq/faq.component';
import globalConfig from 'globalConfig';

export interface ContentCluster {
  mainKeyword: string;
  supportingKeywords: string[];
  contentPieces: ContentPiece[];
  internalLinks: InternalLink[];
}

export interface ContentPiece {
  title: string;
  url: string;
  type: 'landing' | 'category' | 'novel' | 'chapter' | 'blog';
  keywords: string[];
  description: string;
  priority: number;
}

export interface InternalLink {
  fromUrl: string;
  toUrl: string;
  anchorText: string;
  context: string;
}

@Injectable({
  providedIn: 'root'
})
export class ContentStrategyService {
  
  constructor(
    private seoService: SeoService,
    private contentFreshnessService: ContentFreshnessService
  ) {}

  /**
   * Generate content clusters for main keywords
   */
  generateContentClusters(): ContentCluster[] {
    return [
      this.createTruyenChuOnlineCluster(),
      this.createNgonTinhCluster(),
      this.createTienHiepCluster(),
      this.createKiemHiepCluster(),
      this.createDoThiCluster(),
      this.createHuyenHuyenCluster(),
      this.createXuyenKhongCluster()
    ];
  }

  private createTruyenChuOnlineCluster(): ContentCluster {
    return {
      mainKeyword: 'đọc truyện chữ online',
      supportingKeywords: [
        'truyện chữ miễn phí',
        'đọc truyện online',
        'kho truyện chữ',
        'truyện hay 2025',
        'website đọc truyện'
      ],
      contentPieces: [
        {
          title: 'Đọc Truyện Chữ Online Miễn Phí - Kho Truyện Hay Nhất 2025',
          url: '/truyen-chu-online',
          type: 'landing',
          keywords: ['đọc truyện chữ online', 'truyện chữ miễn phí', 'kho truyện hay'],
          description: 'Landing page chính cho từ khóa đọc truyện chữ online',
          priority: 1.0
        },
        {
          title: 'Top 100 Truyện Chữ Hay Nhất Mọi Thời Đại',
          url: '/top-truyen-hay-nhat',
          type: 'category',
          keywords: ['truyện hay nhất', 'top truyện chữ', 'truyện kinh điển'],
          description: 'Danh sách truyện chữ hay nhất được bình chọn',
          priority: 0.9
        }
      ],
      internalLinks: [
        {
          fromUrl: '/',
          toUrl: '/truyen-chu-online',
          anchorText: 'đọc truyện chữ online miễn phí',
          context: 'Khám phá kho truyện chữ khổng lồ'
        }
      ]
    };
  }

  private createNgonTinhCluster(): ContentCluster {
    return {
      mainKeyword: 'truyện ngôn tình',
      supportingKeywords: [
        'ngôn tình hay',
        'truyện tình cảm',
        'ngôn tình hiện đại',
        'ngôn tình cổ đại',
        'ngôn tình CEO'
      ],
      contentPieces: [
        {
          title: 'Truyện Ngôn Tình Hay Nhất - Đọc Online Miễn Phí',
          url: '/the-loai/ngon-tinh',
          type: 'category',
          keywords: ['truyện ngôn tình', 'ngôn tình hay', 'truyện tình cảm'],
          description: 'Trang thể loại ngôn tình với nội dung phong phú',
          priority: 0.9
        }
      ],
      internalLinks: []
    };
  }

  private createTienHiepCluster(): ContentCluster {
    return {
      mainKeyword: 'truyện tiên hiệp',
      supportingKeywords: [
        'tiên hiệp hay',
        'tu tiên',
        'tu ma',
        'tiên hiệp full',
        'truyện tu tiên'
      ],
      contentPieces: [
        {
          title: 'Truyện Tiên Hiệp Hay Nhất - Tu Tiên Tu Ma',
          url: '/the-loai/tien-hiep',
          type: 'category',
          keywords: ['truyện tiên hiệp', 'tiên hiệp hay', 'tu tiên'],
          description: 'Thế giới tiên hiệp huyền bí với tu tiên tu ma',
          priority: 0.9
        }
      ],
      internalLinks: []
    };
  }

  private createKiemHiepCluster(): ContentCluster {
    return {
      mainKeyword: 'truyện kiếm hiệp',
      supportingKeywords: [
        'kiếm hiệp hay',
        'võ lâm',
        'giang hồ',
        'kiếm hiệp cổ điển',
        'truyện võ hiệp'
      ],
      contentPieces: [
        {
          title: 'Truyện Kiếm Hiệp Hay Nhất - Võ Lâm Giang Hồ',
          url: '/the-loai/kiem-hiep',
          type: 'category',
          keywords: ['truyện kiếm hiệp', 'kiếm hiệp hay', 'võ lâm'],
          description: 'Thế giới võ lâm giang hồ với những cao thủ kiếm hiệp',
          priority: 0.9
        }
      ],
      internalLinks: []
    };
  }

  private createDoThiCluster(): ContentCluster {
    return {
      mainKeyword: 'truyện đô thị',
      supportingKeywords: [
        'đô thị hay',
        'truyện hiện đại',
        'đô thị CEO',
        'truyện đời thường',
        'đô thị hệ thống'
      ],
      contentPieces: [
        {
          title: 'Truyện Đô Thị Hay Nhất - Cuộc Sống Hiện Đại',
          url: '/the-loai/do-thi',
          type: 'category',
          keywords: ['truyện đô thị', 'đô thị hay', 'truyện hiện đại'],
          description: 'Câu chuyện cuộc sống đô thị hiện đại',
          priority: 0.9
        }
      ],
      internalLinks: []
    };
  }

  private createHuyenHuyenCluster(): ContentCluster {
    return {
      mainKeyword: 'truyện huyền huyễn',
      supportingKeywords: [
        'huyền huyễn hay',
        'thế giới khác',
        'ma pháp',
        'kỳ ảo',
        'huyền huyễn tây phương'
      ],
      contentPieces: [
        {
          title: 'Truyện Huyền Huyễn Hay Nhất - Thế Giới Kỳ Ảo',
          url: '/the-loai/huyen-huyen',
          type: 'category',
          keywords: ['truyện huyền huyễn', 'huyền huyễn hay', 'thế giới khác'],
          description: 'Thế giới huyền huyễn kỳ ảo với ma pháp và phép thuật',
          priority: 0.9
        }
      ],
      internalLinks: []
    };
  }

  private createXuyenKhongCluster(): ContentCluster {
    return {
      mainKeyword: 'truyện xuyên không',
      supportingKeywords: [
        'xuyên không hay',
        'xuyên việt',
        'trọng sinh',
        'du hành thời gian',
        'xuyên không cổ đại'
      ],
      contentPieces: [
        {
          title: 'Truyện Xuyên Không Hay Nhất - Du Hành Thời Gian',
          url: '/the-loai/xuyen-khong',
          type: 'category',
          keywords: ['truyện xuyên không', 'xuyên không hay', 'xuyên việt'],
          description: 'Hành trình xuyên việt qua các thời đại',
          priority: 0.9
        }
      ],
      internalLinks: []
    };
  }

  /**
   * Generate FAQs for specific content types
   */
  generateGenericFAQs(): FAQItem[] {
    return [
      {
        question: 'Website đọc truyện chữ online nào tốt nhất hiện nay?',
        answer: `<strong>SayTruyenHot</strong> được đánh giá là một trong những website đọc truyện chữ online tốt nhất hiện nay với <strong>hơn 50,000+ bộ truyện</strong> đa dạng thể loại, giao diện thân thiện, cập nhật nhanh và hoàn toàn miễn phí.`,
        category: 'general',
        keywords: ['website đọc truyện', 'truyện chữ online', 'saytruyenhot']
      },
      {
        question: 'Có thể đọc truyện chữ miễn phí ở đâu?',
        answer: `Tại <strong>SayTruyenHot</strong>, bạn có thể đọc <strong>hoàn toàn miễn phí</strong> hàng chục nghìn bộ truyện chữ thuộc mọi thể loại. Chúng tôi không thu phí đọc truyện và cam kết mang đến trải nghiệm đọc tốt nhất cho độc giả.`,
        category: 'pricing',
        keywords: ['đọc truyện miễn phí', 'truyện chữ free', 'không mất phí']
      },
      {
        question: 'Làm sao để tìm truyện chữ hay phù hợp với sở thích?',
        answer: `SayTruyenHot cung cấp <strong>hệ thống tìm kiếm nâng cao</strong> với nhiều bộ lọc: thể loại, tác giả, trạng thái, số chương. Bạn cũng có thể xem <strong>bảng xếp hạng</strong> các truyện hot nhất theo ngày, tuần, tháng để tìm truyện phù hợp.`,
        category: 'search',
        keywords: ['tìm truyện hay', 'lọc truyện', 'bảng xếp hạng']
      },
      {
        question: 'Truyện chữ được cập nhật bao lâu một lần?',
        answer: `Tại SayTruyenHot, truyện được cập nhật <strong>24/7 liên tục</strong>. Mỗi ngày có hàng trăm chương mới được đăng tải. Bạn có thể theo dõi truyện yêu thích để nhận thông báo ngay khi có chương mới.`,
        category: 'updates',
        keywords: ['cập nhật truyện', 'chương mới', 'theo dõi truyện']
      },
      {
        question: 'Có thể đọc truyện trên điện thoại không?',
        answer: `Có, SayTruyenHot được tối ưu hoàn hảo cho <strong>mọi thiết bị</strong> bao gồm điện thoại, máy tính bảng và máy tính. Giao diện responsive giúp bạn đọc truyện mượt mà trên mọi kích thước màn hình.`,
        category: 'mobile',
        keywords: ['đọc truyện mobile', 'responsive', 'điện thoại']
      }
    ];
  }

  /**
   * Generate internal linking suggestions
   */
  generateInternalLinkingSuggestions(currentNovel: Novel): InternalLink[] {
    const links: InternalLink[] = [];
    
    // Same author links
    if (currentNovel.author) {
      links.push({
        fromUrl: `/truyen/${currentNovel.url}-${currentNovel.id}`,
        toUrl: `/tim-kiem?author=${encodeURIComponent(currentNovel.author)}`,
        anchorText: `truyện khác của ${currentNovel.author}`,
        context: `Khám phá thêm những tác phẩm hay khác của tác giả ${currentNovel.author}`
      });
    }

    // Same genre links
    currentNovel.genres.forEach(genre => {
      links.push({
        fromUrl: `/truyen/${currentNovel.url}-${currentNovel.id}`,
        toUrl: `/the-loai/${genre.slug}`,
        anchorText: `truyện ${genre.title} hay khác`,
        context: `Đọc thêm những bộ truyện ${genre.title} hấp dẫn khác`
      });
    });

    // Status-based links
    if (currentNovel.status === 1) {
      links.push({
        fromUrl: `/truyen/${currentNovel.url}-${currentNovel.id}`,
        toUrl: '/tim-kiem?status=1',
        anchorText: 'truyện đã hoàn thành khác',
        context: 'Khám phá thêm những bộ truyện full hay khác'
      });
    }

    return links;
  }

  /**
   * Generate content calendar for SEO
   */
  generateContentCalendar(): {
    daily: string[];
    weekly: string[];
    monthly: string[];
  } {
    return this.contentFreshnessService.generateContentCalendar();
  }

  /**
   * Get content optimization suggestions
   */
  getContentOptimizationSuggestions(contentType: string, content: any): {
    titleSuggestions: string[];
    descriptionSuggestions: string[];
    keywordSuggestions: string[];
    internalLinkSuggestions: InternalLink[];
  } {
    switch (contentType) {
      case 'novel':
        return this.getNovelOptimizationSuggestions(content as Novel);
      case 'genre':
        return this.getGenreOptimizationSuggestions(content as Genre);
      case 'chapter':
        return this.getChapterOptimizationSuggestions(content as Chapter);
      default:
        return {
          titleSuggestions: [],
          descriptionSuggestions: [],
          keywordSuggestions: [],
          internalLinkSuggestions: []
        };
    }
  }

  private getNovelOptimizationSuggestions(novel: Novel): {
    titleSuggestions: string[];
    descriptionSuggestions: string[];
    keywordSuggestions: string[];
    internalLinkSuggestions: InternalLink[];
  } {
    const genreNames = novel.genres.map(g => g.title).join(', ');
    
    return {
      titleSuggestions: [
        `Truyện ${novel.title} - ${novel.author} | Đọc Online Miễn Phí`,
        `${novel.title} Full - ${genreNames} Hay Nhất`,
        `Đọc Truyện ${novel.title} ${novel.status === 1 ? 'Hoàn Thành' : 'Mới Nhất'}`
      ],
      descriptionSuggestions: [
        `Đọc truyện ${novel.title} của tác giả ${novel.author} thuộc thể loại ${genreNames}. ${novel.status === 1 ? 'Truyện đã hoàn thành' : 'Cập nhật mới nhất'} tại SayTruyenHot.`,
        `${novel.title} - ${genreNames} hay nhất của ${novel.author}. ${novel.numChapter} chương, đọc miễn phí tại SayTruyenHot.`
      ],
      keywordSuggestions: [
        `truyện ${novel.title}`,
        novel.author || '',
        genreNames,
        novel.status === 1 ? 'truyện full' : 'truyện hot',
        'đọc truyện online',
        'saytruyenhot'
      ].filter(Boolean),
      internalLinkSuggestions: this.generateInternalLinkingSuggestions(novel)
    };
  }

  private getGenreOptimizationSuggestions(genre: Genre): {
    titleSuggestions: string[];
    descriptionSuggestions: string[];
    keywordSuggestions: string[];
    internalLinkSuggestions: InternalLink[];
  } {
    return {
      titleSuggestions: [
        `Truyện ${genre.title} Hay Nhất | Đọc Online Miễn Phí`,
        `Top ${genre.title} Hot Nhất 2025 - SayTruyenHot`,
        `Kho Truyện ${genre.title} Lớn Nhất Việt Nam`
      ],
      descriptionSuggestions: [
        `Đọc truyện ${genre.title} hay nhất, cập nhật liên tục. Hàng nghìn bộ truyện ${genre.title} chất lượng cao, đọc miễn phí tại SayTruyenHot.`,
        `Kho truyện ${genre.title} lớn nhất với những tác phẩm hay nhất, hot nhất. Cập nhật 24/7, đọc mượt mà trên mọi thiết bị.`
      ],
      keywordSuggestions: [
        `truyện ${genre.title}`,
        `${genre.title} hay`,
        `đọc truyện ${genre.title} online`,
        `${genre.title} miễn phí`,
        `top ${genre.title}`,
        `kho truyện ${genre.title}`
      ],
      internalLinkSuggestions: []
    };
  }

  private getChapterOptimizationSuggestions(chapter: Chapter): {
    titleSuggestions: string[];
    descriptionSuggestions: string[];
    keywordSuggestions: string[];
    internalLinkSuggestions: InternalLink[];
  } {
    return {
      titleSuggestions: [
        `${chapter.novel?.title} - Chương ${chapter.slug} | Đọc Online Miễn Phí`,
        `Chương ${chapter.slug}: ${chapter.novel?.title} - ${chapter.novel?.author}`
      ],
      descriptionSuggestions: [
        `Chương ${chapter.slug}: Đọc truyện "${chapter.novel?.title}"${chapter.novel?.author ? ' của ' + chapter.novel.author : ''}. Nội dung mới nhất, chất lượng, cập nhật liên tục tại SayTruyenHot.`
      ],
      keywordSuggestions: [
        `${chapter.novel?.title} chương ${chapter.slug}`,
        `đọc truyện ${chapter.novel?.title}`,
        chapter.novel?.author || '',
        'truyện online',
        'chương mới'
      ].filter(Boolean),
      internalLinkSuggestions: []
    };
  }
}
