.user-info-modal {
    @apply fixed max-[376px]:w-[80%] max-sm:w-[300px] bg-white text-black dark:text-white dark:bg-neutral-800 shadow-[0px_0px_7px_7px_rgba(0,0,0,0.1)] left-1/2 top-1/2 z-50 w-[400px] block p-4 rounded-lg transform -translate-x-1/2 -translate-y-1/2;
  }

  .user-info-loading {
    @apply flex-center h-full;
  }

  .loading-spinner {
    @apply inline-flex items-center gap-3 rounded-md px-5 py-3 ;
  }

  .user-info-header {
    @apply flex justify-between items-center text-lg;
  }

  .user-info-title {
    @apply font-bold text-base flex-start;
  }

  .user-info-close {
    @apply cursor-pointer;
  }

  .user-info-content {
    @apply flex flex-col sm:flex-row justify-between border-y-2 border-dashed border-gray-200 py-2;
  }

  .user-info-avatar {
    @apply w-full m-auto max-sm:mt-2 sm:w-[30%] flex flex-col items-center;
  }

  .user-info-level {
    @apply absolute p-2 flex-center -top-2 w-10 h-10 -right-5 rounded-full border-2;
  }

  .user-info-details {
    @apply w-full sm:w-[60%] max-sm:ml-2 flex flex-col items-start mt-2 sm:mt-0 sm:pl-4;
  }