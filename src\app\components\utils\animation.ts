import { trigger, transition, style, animate } from '@angular/animations';

export const fadeInOut = trigger('fadeInOut', [
    // <PERSON>hi phần tử xuất hiện (fade-in)
    transition(':enter', [
        style({ opacity: 0, transform: 'scaleY(0)', }), // Bắt đầu với scaleY(0) và opacity 0
        animate('100ms ease-out', style({ opacity: 1, transform: 'scaleY(1)', })) // Phóng to theo chiều dọc từ trên
    ]),

    // <PERSON>hi phần tử rời khỏi (fade-out)
    transition(':leave', [
        animate('100ms ease-in', style({ opacity: 0})) // Thu nhỏ theo chiều dọc từ trên
    ])
]);

export const openDialog = trigger('openDialog', [
    transition(':enter', [
        style({ opacity: 0,}), // Bắt đầu với scaleY(0) và opacity 0
        animate('100ms ease-in', style({ opacity: 1, })) // Phóng to theo chiều dọc từ trên
    ]),
    transition(':leave', [
        animate('100ms ease-out', style({ opacity: 0 })) // Thu nhỏ theo chiều dọc từ trên
    ])
])

export const slideOut = trigger('slideOut', [
    
])

