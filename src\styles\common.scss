.btn {
    @apply inline-flex justify-center items-center gap-2 py-2.5 px-4 rounded-lg font-medium transition-all duration-300 ease-in-out border-none cursor-pointer no-underline;
}

.btn-primary {
    @apply bg-primary-100 text-white;
}

.btn-primary:hover {
    @apply bg-primary-200;
}

.btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700;
}

.btn-danger {
    @apply bg-red-500 text-white hover:bg-red-600;
}

.btn-success {
    @apply bg-green-500 text-white hover:bg-green-600;
}

.btn-warning {
    @apply bg-yellow-500 text-white hover:bg-yellow-600;
}

.btn-outline {
    @apply border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800;
}

.btn-outline-primary {
    @apply border border-primary-100 bg-transparent text-primary-100 hover:bg-primary-100 hover:text-white;
}

.btn-sm {
    @apply text-sm px-3 py-1.5;
}

.btn-lg {
    @apply text-lg px-8 py-4;
}

.btn-icon {
    @apply p-2 w-auto h-auto;
}

.btn-full {
    @apply w-full;
}

.btn-disabled {
    @apply opacity-50 cursor-not-allowed pointer-events-none;
}

/* === FORM STYLES === */
.form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-100 focus:border-primary-100 dark:bg-dark-700 dark:border-gray-600 dark:text-white dark:focus:border-primary-100;
}

.form-input-error {
    @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
}

.form-label-required::after {
    @apply text-red-500;
    content: ' *';
}

.form-textarea {
    @apply form-input min-h-20 resize-y;
}

.form-select {
    @apply form-input cursor-pointer;
}

.form-group {
    @apply mb-4;
}

.form-error {
    @apply text-red-500 text-sm mt-1;
}

.form-help {
    @apply text-gray-500 text-sm mt-1;
}

/* === LAYOUT STYLES === */
.container-fluid {
    @apply w-full px-4;
}

.container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.container-sm {
    @apply max-w-3xl mx-auto px-4;
}

.container-xs {
    @apply max-w-md mx-auto px-4;
}

.section-padding {
    @apply py-8 lg:py-12;
}

.section-margin {
    @apply my-8 lg:my-12;
}

/* === TEXT STYLES === */
.text-gradient {
    @apply bg-gradient-to-r from-primary-100 to-primary-200 bg-clip-text text-transparent;
}

.text-muted {
    @apply text-gray-600 dark:text-gray-400;
}

.text-error {
    @apply text-red-500;
}

.text-success {
    @apply text-green-500;
}

.text-warning {
    @apply text-yellow-500;
}

.text-link {
    @apply text-primary-100 hover:text-primary-200 transition-colors duration-200 cursor-pointer;
}

.text-underline {
    @apply underline decoration-2 underline-offset-2;
}

.text-truncate-1 {
    @apply line-clamp-1;
}

.text-truncate-2 {
    @apply line-clamp-2;
}

.text-truncate-3 {
    @apply line-clamp-3;
}

.text-truncate-4 {
    @apply line-clamp-4;
}

/* === SPACING STYLES === */
.space-section {
    @apply space-y-8;
}

.space-content {
    @apply space-y-4;
}

.space-items {
    @apply space-y-2;
}

.gap-section {
    @apply gap-8;
}

.gap-content {
    @apply gap-4;
}

.gap-items {
    @apply gap-2;
}

/* === BORDER & DIVIDER STYLES === */
.border-primary {
    @apply border-primary-100;
}

.border-dashed-primary {
    @apply border border-dashed border-primary-100;
}

.divider {
    @apply border-t border-gray-200 dark:border-gray-600 my-4;
}

.divider-vertical {
    @apply border-l border-gray-200 dark:border-gray-600 mx-4;
}

.divider-thick {
    @apply border-t-2 border-gray-300 dark:border-gray-500 my-6;
}

/* === SHADOW STYLES === */
.shadow-primary {
    @apply shadow-lg;
    box-shadow: 0 10px 15px -3px rgba(227, 95, 102, 0.2), 0 4px 6px -2px rgba(227, 95, 102, 0.1);
}

.shadow-hover {
    @apply transition-shadow duration-300 hover:shadow-lg;
}

.shadow-float {
    @apply shadow-xl;
}

/* === ANIMATION STYLES === */
.animate-fade-in {
    @apply opacity-0 animate-pulse;
    animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.animate-slide-up {
    @apply translate-y-4 opacity-0;
    animation: slideUp 0.3s ease-in-out forwards;
}

@keyframes slideUp {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-slide-down {
    @apply -translate-y-4 opacity-0;
    animation: slideDown 0.3s ease-in-out forwards;
}

@keyframes slideDown {
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.animate-scale-in {
    @apply scale-95 opacity-0;
    animation: scaleIn 0.2s ease-in-out forwards;
}

@keyframes scaleIn {
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.transition-default {
    @apply transition-all duration-300 ease-in-out;
}

.transition-fast {
    @apply transition-all duration-150 ease-in-out;
}

.transition-slow {
    @apply transition-all duration-500 ease-in-out;
}

/* === LOADING & SKELETON STYLES === */
.loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-100;
}

.skeleton {
    @apply animate-pulse bg-gray-300 dark:bg-gray-600 rounded;
}

.skeleton-text {
    @apply skeleton h-4 w-full;
}

.skeleton-title {
    @apply skeleton h-6 w-3/4;
}

.skeleton-avatar {
    @apply skeleton rounded-full;
}

.skeleton-button {
    @apply skeleton h-10 w-24;
}

/* === STATUS STYLES === */
.status-online {
    @apply bg-green-500;
}

.status-offline {
    @apply bg-gray-400;
}

.status-busy {
    @apply bg-red-500;
}

.status-away {
    @apply bg-yellow-500;
}

.status-dot {
    @apply inline-block w-2 h-2 rounded-full;
}

.status-indicator {
    @apply status-dot absolute -top-0.5 -right-0.5 border border-white;
}

/* === UTILITY STYLES === */
.aspect-square {
    @apply aspect-[1/1];
}

.aspect-video {
    @apply aspect-[16/9];
}

.aspect-photo {
    @apply aspect-[4/3];
}

.aspect-book {
    @apply aspect-[3/4];
}

.blur-backdrop {
    @apply backdrop-blur-sm bg-white/80 dark:bg-dark-800/80;
}

.glassmorphism {
    @apply backdrop-blur-md bg-white/10 dark:bg-white/5 border border-white/20;
}

.overflow-fade {
    @apply relative;
}

.overflow-fade::after {
    @apply absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white dark:from-dark-700 to-transparent pointer-events-none;
    content: '';
}

.scrollbar-hide {
    @apply no-scrollbar;
}

.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-100 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-dark-800;
}

.interactive-element {
    @apply cursor-pointer select-none transition-all duration-200 ease-in-out;
}

.interactive-element:hover {
    @apply scale-105;
}

.interactive-element:active {
    @apply scale-95;
}

/* === RESPONSIVE HELPERS === */
.mobile-only {
    @apply block sm:hidden;
}

.tablet-up {
    @apply hidden sm:block;
}

.desktop-only {
    @apply hidden lg:block;
}

.mobile-tablet {
    @apply block lg:hidden;
}

/* === DARK MODE HELPERS === */
.dark-invert {
    @apply dark:invert;
}

.dark-brightness {
    @apply dark:brightness-75;
}

/* === POSITIONING HELPERS === */
.center-absolute {
    @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
}

.center-fixed {
    @apply fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
}

.top-right {
    @apply absolute top-0 right-0;
}

.top-left {
    @apply absolute top-0 left-0;
}

.bottom-right {
    @apply absolute bottom-0 right-0;
}

.bottom-left {
    @apply absolute bottom-0 left-0;
}


