<div class="related-content" *ngIf="relatedSections.length > 0">
  <section 
    *ngFor="let section of relatedSections; trackBy: trackBySection"
    class="related-section"
    [class.has-novels]="hasValidNovels(section)"
  >
    <div class="section-header">
      <h3 class="section-title">{{ section.title }}</h3>
      <p class="section-description" *ngIf="getSectionDescription(section)">
        {{ getSectionDescription(section) }}
      </p>
    </div>

    <div class="novels-container" *ngIf="hasValidNovels(section)">
      <div class="novels-grid">
        <app-card-v1 
          *ngFor="let novel of getDisplayNovels(section); trackBy: trackByNovel"
          [novel]="novel"
          class="novel-item"
        ></app-card-v1>
      </div>

      <div class="section-footer" *ngIf="shouldShowViewAll(section)">
        <a 
          [routerLink]="section.viewAllLink"
          class="view-all-link"
          [title]="'Xem tất cả ' + section.title.toLowerCase()"
        >
          Xem tất cả {{ section.novels.length }} truyện →
        </a>
      </div>
    </div>

    <div class="empty-section" *ngIf="!hasValidNovels(section)">
      <p class="empty-message">Không có truyện nào trong mục này</p>
    </div>
  </section>
</div>

<div class="no-related-content" *ngIf="relatedSections.length === 0">
  <div class="no-content-message">
    <h3>Nội dung liên quan</h3>
    <p>Hiện tại chưa có nội dung liên quan. Hãy khám phá thêm các truyện khác!</p>
    <a routerLink="/" class="explore-link">
      Khám phá truyện hay →
    </a>
  </div>
</div>
