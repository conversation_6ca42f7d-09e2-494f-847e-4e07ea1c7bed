<div class="w-full flex gap-4 justify-center items-center">
  <label class="checkbox-container" *ngFor="let option of options">
    <input
      class="custom-checkbox"
      [checked]="option.value === value"
      type="radio"
      [name]="name"
      [value]="option.value"
      (change)="onColorChange(option.value)"
    />
    <span class="checkmark" [style.background-color]="option.value"></span>
  </label>
</div>
