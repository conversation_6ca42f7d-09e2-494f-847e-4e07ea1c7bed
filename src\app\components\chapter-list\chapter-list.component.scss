// .txt-rank {
//     position: relative;
//     font-weight: bold;
//     color: white;
//     left: 13.5px;
//     text-align: center;
//     height: 30px;
//     width: 30px;
//     z-index: 100; // or any other value you prefer
//   }

/* filepath: /d:/Project/novel-client/src/app/modules/novel-detail/page/list-chapter/chapter-list.component.scss */
.chapter-panel {
  @apply flex-between flex-col sm:flex-row gap-2;
}

.chapter-title {
  @apply font-semibold uppercase flex-start;
}

.chapter-title-text {
  @apply ml-2 text-xl;
}

.chapter-controls {
  @apply flex-center;
}

.chapter-search-container {
  @apply relative;
}

.chapter-search-icon {
  @apply absolute inset-y-0 start-0 flex items-center px-3 pointer-events-none;
}

.chapter-search-svg {
  @apply w-4 h-4 text-gray-400 dark:text-gray-400;
}

.chapter-selection {
  @apply w-32 ml-2;
}

.chapter-not-found {
  @apply font-thin bg-gray-100 dark:bg-neutral-800 h-20 rounded-lg my-3 p-3 text-gray-600 dark:text-gray-200;
}

.chapter-list-container {
  @apply size-full min-h-48 relative overflow-y-auto overflow-x-hidden mt-2;
}

.chapter-list-overlay {
  @apply absolute top-0 right-0 -z-50 left-0;
}

.chapter-list {
  @apply grid grid-cols-1  relative mr-2;
}

.chapter-item {
  @apply hover:bg-neutral-100 dark:hover:bg-neutral-700 gap-1 text-nowrap border-b-[1px] border-dashed dark:border-dark-500 duration-75 h-10 flex-start  text-sm  py-1 cursor-pointer select-none;
}

.chapter-item-read {
  @apply bg-gray-200 dark:bg-neutral-800;
}

.chapter-item-content {
  @apply p-1;
}

.chapter-item-title {
  @apply font-medium;
}

.chapter-item-date {
  @apply ml-auto p-1;
}

.chapter-item-date-text {
  @apply text-gray-500 dark:text-gray-300 text-xs;
}

.chapter-loading {
  @apply absolute top-0 right-0 size-full min-h-48 flex-center ;
}