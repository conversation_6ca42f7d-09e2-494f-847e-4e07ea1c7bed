import { Component, EventEmitter, Output } from '@angular/core';
import { EMOJI_CONTENTS } from '../utils/constants';

@Component({
    selector: 'app-emoji',
    templateUrl: './emoji.component.html',
    styleUrl: './emoji.component.scss',
    standalone: false
})
export class EmojiComponent {
  emoji_contents = EMOJI_CONTENTS;
  activate: number = 1;
  isPopoverOpen = false;

  @Output() emojiSelect = new EventEmitter<{ name: string; path: string }>();

  selectPacket(activate_id: number): void {
    this.activate = activate_id;
    if (this.isPopoverOpen) {
      this.togglePopover();
    }
  }

  selectEmoji(name: string, path: string): void {
    this.emojiSelect.emit({ name, path });
  }

  handleEmojiClick(index: number): void {
    const activeEmoji = this.emoji_contents.find(
      (content) => content.id === this.activate
    );
    if (!activeEmoji) return;

    const name = this.getAttrTarget(activeEmoji.name, index);
    const path = this.getEmojiPath(this.activate, index);

    this.selectEmoji(name, path);
  }

  getAttrTarget(name: string, index: number): string {
    return `${name}_${index}`;
  }

  togglePopover(event?: Event): void {
    if (event) {
      event.stopPropagation(); // Ngăn sự kiện lan lên cha
    }
    this.isPopoverOpen = !this.isPopoverOpen;
  }

  getEmojiPath(activate: number, index: number): string {
    const activeEmoji = this.emoji_contents.find(
      (content) => content.id === activate
    );
    if (!activeEmoji || index <= 0 || index > activeEmoji.length) return '';

    return `/assets/emoji/data/${activeEmoji.name}/${index}.gif`;
  }

  getEmojiList(): number[] {
    const activeEmoji = this.emoji_contents.find(
      (content) => content.id === this.activate
    );
    return activeEmoji
      ? Array.from({ length: activeEmoji.length }, (_, i) => i + 1)
      : [];
  }
}
