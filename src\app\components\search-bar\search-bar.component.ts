import { CommonModule } from '@angular/common';
import {
  Component,
  ElementRef,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NovelService } from '@services/novel.service';


@Component({
  selector: 'app-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrl: './search-bar.component.scss',
  imports: [CommonModule, FormsModule],
  encapsulation: ViewEncapsulation.None,
})
export class SearchBarComponent {
  isSearching = false;
  searchText = '';
  @ViewChild('SearchInput') SearchInput!: ElementRef;
  @ViewChild('SearchFrame') SearchFrame!: ElementRef;
  trackByFn = (index: number) => index;

  constructor(private novelService: NovelService, private router: Router) { }

  // SendSearchReq() {
  //   if (this.searchText != '') {
  //     this.novelService
  //       .getSearchNovel(this.searchText)
  //       .subscribe((res: IServiceResponse<Novel[]>) => {
  //         this.isLoading = false;

  //       });
  //   } else {
  //     this.isLoading = false;

  //   }
  // }
  // OnSearchChange() {
  //   this.isLoading = true;
  //   clearTimeout(this.typingTimer);
  //   this.typingTimer = setTimeout(() => {
  //     this.searchText = this.SearchInput.nativeElement.value;
  //     this.SendSearchReq();
  //   }, this.doneTypingInterval);
  // }
  OnSearchFocus = (isFocus: boolean): boolean => {
    this.isSearching = isFocus;
    if (isFocus) {
      this.SearchInput.nativeElement.classList.add('!w-full');
    } else {
      this.SearchFrame.nativeElement.classList.add('hidden');
      this.SearchInput.nativeElement.classList.remove('!w-full');
    }
    return true;
  };
  OnSearchClick = (): boolean => {
    this.SearchFrame.nativeElement.classList.remove('hidden');
    this.SearchInput.nativeElement.focus();
    // router.navigate(['search']);
    return true;
  };

  OnSearchEnter() {
    if (this.searchText == '') return;
    this.router.navigate(['tim-kiem'], { queryParams: { keyword: this.searchText } });
    this.clearSearch();
    this.OnSearchFocus(false);
  }

  clearSearch(): void {
    this.searchText = '';
    this.SearchInput.nativeElement.value = '';

    // this.listSearch = [];
  }
}
