import { Component, OnInit } from '@angular/core';
import { User } from '@schemas/User';
import { AccountService } from '@services/account.service';
import { ToastService, ToastType } from '@services/toast.service';

@Component({
  selector: 'app-user-settings',
  templateUrl: './user-settings.component.html',
  styleUrls: ['./user-settings.component.scss'],
  standalone: false
})
export class UserSettingsComponent implements OnInit {
  
  user!: User;
  
  // Settings sections
  activeSection: 'profile' | 'privacy' | 'notifications' | 'preferences' = 'profile';
  
  // Profile settings
  profileForm = {
    firstName: '',
    lastName: '',
    email: '',
    maxim: '',
    dob: ''
  };

  // Privacy settings
  privacySettings = {
    profileVisibility: 'public', // public, friends, private
    showReadingHistory: true,
    showFavorites: true
  };

  // Notification settings  
  notificationSettings = {
    emailNotifications: true,
    newChapterAlerts: true,
    favoriteUpdates: true,
    systemUpdates: false
  };

  // Reading preferences
  readingPreferences = {
    theme: 'light', // light, dark, auto
    fontSize: 'medium', // small, medium, large
    fontFamily: 'default', // default, serif, sans-serif
    lineHeight: 'normal' // compact, normal, relaxed
  };

  constructor(
    private accountService: AccountService,
    private toast: ToastService
  ) {}

  ngOnInit(): void {
    this.loadUserData();
  }

  loadUserData() {
    this.accountService.GetUserInfo().subscribe((res: any) => {
      if (res.status) {
        this.user = res.data;
        this.initializeForms();
      }
    });
  }

  initializeForms() {
    this.profileForm = {
      firstName: this.user.firstName || '',
      lastName: this.user.lastName || '',
      email: this.user.email || '',
      maxim: this.user.maxim || '',
      dob: this.user.dob || ''
    };
  }

  switchSection(section: 'profile' | 'privacy' | 'notifications' | 'preferences') {
    this.activeSection = section;
  }

  updateProfile() {
    // Implement profile update logic
    this.toast.show(ToastType.Success, 'Cập nhật thông tin thành công!');
  }

  updatePrivacySettings() {
    // Implement privacy settings update
    this.toast.show(ToastType.Success, 'Cập nhật cài đặt riêng tư thành công!');
  }

  updateNotificationSettings() {
    // Implement notification settings update  
    this.toast.show(ToastType.Success, 'Cập nhật cài đặt thông báo thành công!');
  }

  updateReadingPreferences() {
    // Implement reading preferences update
    this.toast.show(ToastType.Success, 'Cập nhật cài đặt đọc truyện thành công!');
  }

  changePassword() {
    // Implement password change logic
    this.toast.show(ToastType.Info, 'Tính năng đổi mật khẩu đang được phát triển!');
  }

  deleteAccount() {
    if (confirm('Bạn có chắc chắn muốn xóa tài khoản? Hành động này không thể hoàn tác!')) {
      // Implement account deletion logic
      this.toast.show(ToastType.Info, 'Tính năng xóa tài khoản đang được phát triển!');
    }
  }
}
