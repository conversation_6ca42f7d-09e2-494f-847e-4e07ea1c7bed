export const SEO_CONFIG = {
  // Core SEO Settings
  SITE_NAME: 'SayTruyenHot',
  BASE_URL: 'https://saytruyenhot.com',
  DEFAULT_TITLE: 'Đọ<PERSON>ru<PERSON> Chữ Online Miễn Phí | Kho Truyện Hay Nhất Việt Nam',
  DEFAULT_DESCRIPTION: 'Đọc truyện chữ online miễn phí với hơn 50,000+ truyện ngôn tình, tiên hiệ<PERSON>, kiếm hiệp, đô thị, huyền huyễn hay nhất. Cập nhật 24/7, không quảng cáo, đọc mượt mà trên mọi thiết bị.',
  DEFAULT_KEYWORDS: 'đọc truyện chữ online, truyện chữ miễn phí, ngôn tình hay, tiên hiệp full, kiếm hiệp hot, truyện xuyên không, đô thị huyền huyễn, truyện audio, saytruyenhot, kho tru<PERSON>ện lớn, cập nh<PERSON>t hàng ngày, tru<PERSON><PERSON><PERSON> hay nhất, tru<PERSON><PERSON>n full hoàn thành, đọc truyện không quảng cáo',
  DEFAULT_IMAGE: '/logo.png',
  THEME_COLOR: '#1f2937',
  
  // Social Media
  TWITTER_HANDLE: '@saytruyenhot',
  FACEBOOK_APP_ID: '',
  
  // Structured Data
  ORGANIZATION: {
    name: 'SayTruyenHot',
    url: 'https://saytruyenhot.com',
    logo: 'https://saytruyenhot.com/logo.png',
    description: 'Website đọc truyện chữ online hàng đầu Việt Nam với kho tàng truyện chữ phong phú',
    foundingDate: '2025',
    areaServed: 'VN',
    serviceType: 'Online Novel Reading Platform'
  },
  
  // Performance Settings
  PRELOAD_IMAGES_COUNT: 3,
  PREFETCH_LINKS_COUNT: 5,
  
  // Core Web Vitals Thresholds
  CORE_WEB_VITALS: {
    LCP_GOOD: 2500,
    LCP_NEEDS_IMPROVEMENT: 4000,
    FID_GOOD: 100,
    FID_NEEDS_IMPROVEMENT: 300,
    CLS_GOOD: 0.1,
    CLS_NEEDS_IMPROVEMENT: 0.25
  },
  
  // Page-specific SEO Templates
  PAGE_TEMPLATES: {
    HOME: {
      titleTemplate: '%s | Kho Truyện Hay Nhất Việt Nam',
      descriptionTemplate: 'Đọc truyện chữ online miễn phí với hơn %d+ truyện hay nhất. %s',
      keywordsTemplate: 'trang chủ, %s, kho truyện lớn, cập nhật hàng ngày'
    },
    NOVEL: {
      titleTemplate: 'Truyện %s - %s | Đọc Online Miễn Phí',
      descriptionTemplate: 'Đọc truyện %s của tác giả %s thuộc thể loại %s. Truyện hay, cập nhật mới nhất tại SayTruyenHot',
      keywordsTemplate: 'truyện %s, %s, %s, đọc truyện online, truyện hay'
    },
    CHAPTER: {
      titleTemplate: '%s - Chương %s | Đọc Online Miễn Phí',
      descriptionTemplate: 'Chương %s: Đọc truyện "%s"%s. Nội dung mới nhất, chất lượng, cập nhật liên tục tại SayTruyenHot.',
      keywordsTemplate: '%s chương %s, đọc truyện %s, %s, truyện online'
    },
    GENRE: {
      titleTemplate: 'Truyện %s Hay Nhất | Đọc Online Miễn Phí',
      descriptionTemplate: 'Đọc truyện %s hay nhất, cập nhật liên tục. Hàng nghìn bộ truyện %s chất lượng cao, đọc miễn phí tại SayTruyenHot.',
      keywordsTemplate: 'truyện %s, %s hay, đọc truyện %s online, %s miễn phí'
    },
    SEARCH: {
      titleTemplate: 'Tìm Kiếm Truyện: %s | SayTruyenHot',
      descriptionTemplate: 'Kết quả tìm kiếm cho "%s". Tìm thấy %d truyện phù hợp với từ khóa của bạn.',
      keywordsTemplate: 'tìm kiếm truyện, %s, kết quả tìm kiếm, truyện hay'
    }
  },
  
  // Robots.txt Rules
  ROBOTS_RULES: {
    ALLOW: [
      '/',
      '/truyen/*',
      '/the-loai/*',
      '/tim-kiem',
      '/ban-quyen',
      '/ve-chung-toi',
      '/dieu-khoan'
    ],
    DISALLOW: [
      '/tu-sach/*',
      '/price/*',
      '/tai-khoan/*',
      '/auth/*',
      '/api/*',
      '/*?*utm_*',
      '/*?*fbclid*',
      '/*?*gclid*'
    ]
  },
  
  // Sitemap Configuration
  SITEMAP: {
    MAIN_PRIORITY: '1.0',
    NOVEL_PRIORITY: '0.75',
    CHAPTER_PRIORITY: '0.55',
    GENRE_PRIORITY: '0.8',
    STATIC_PRIORITY: '0.5',
    CHANGEFREQ: {
      HOME: 'daily',
      NOVEL: 'weekly',
      CHAPTER: 'daily',
      GENRE: 'weekly',
      STATIC: 'monthly'
    }
  },
  
  // Analytics & Tracking
  ANALYTICS: {
    GOOGLE_ANALYTICS_ID: 'G-11QMWS15HV',
    GOOGLE_TAG_MANAGER_ID: '',
    FACEBOOK_PIXEL_ID: '',
    TRACK_CORE_WEB_VITALS: true,
    TRACK_USER_ENGAGEMENT: true
  },
  
  // Image Optimization
  IMAGE_OPTIMIZATION: {
    LAZY_LOADING: true,
    WEBP_SUPPORT: true,
    RESPONSIVE_IMAGES: true,
    DEFAULT_FALLBACK: 'https://static.saytruyenhot.com/coverimg/ban-trai-toi-la-hoc-ba.jpg',
    PRELOAD_HERO_IMAGES: true
  },
  
  // Content Security
  CONTENT_SECURITY: {
    CANONICAL_URLS: true,
    NOINDEX_PATTERNS: ['/tu-sach/', '/tai-khoan/', '/auth/'],
    NOFOLLOW_EXTERNAL: true,
    SECURE_HEADERS: true
  }
};

// Helper functions for SEO
export const SEO_HELPERS = {
  formatTitle: (title: string, template: string = SEO_CONFIG.PAGE_TEMPLATES.HOME.titleTemplate): string => {
    return template.replace('%s', title);
  },
  
  formatDescription: (description: string, ...args: any[]): string => {
    let formatted = description;
    args.forEach((arg, index) => {
      formatted = formatted.replace(`%${index === 0 ? 'd' : 's'}`, arg);
    });
    return formatted;
  },
  
  generateKeywords: (baseKeywords: string, ...additionalKeywords: string[]): string => {
    const allKeywords = [baseKeywords, ...additionalKeywords.filter(Boolean)];
    return allKeywords.join(', ');
  },
  
  isNoIndexPage: (url: string): boolean => {
    return SEO_CONFIG.CONTENT_SECURITY.NOINDEX_PATTERNS.some(pattern => 
      url.includes(pattern)
    );
  },
  
  shouldPreloadImage: (index: number): boolean => {
    return index < SEO_CONFIG.PRELOAD_IMAGES_COUNT;
  }
};
