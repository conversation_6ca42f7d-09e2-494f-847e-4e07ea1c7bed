<div class="pagination-container">
  <span class="w-full border border-t border-neutral-200 dark:border-neutral-800"></span>
  <!-- Main pagination -->
  <nav class="pagination-nav" role="navigation" aria-label="Phân trang">
    <!-- Previous Button -->
    <a
      *ngIf="currentPage > 1"
      [routerLink]="[rootLink, 'trang-' + (currentPage - 1)]"
      [queryParamsHandling]="'merge'"
      class="pagination-btn pagination-prev"
      title="Trang trước"
      aria-label="Đi đến trang trước"
    >
      <svg class="pagination-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="15,18 9,12 15,6"></polyline>
      </svg>
      <span class="pagination-btn-text">Trước</span>
    </a>

    <!-- Page Numbers -->
    <div class="pagination-pages">
      <ng-container *ngFor="let page of pages; trackBy: trackByPage">
        <a
          *ngIf="page.title !== '...'; else ellipsis"
          [routerLink]="[rootLink, page.link]"
          [queryParamsHandling]="'merge'"
          class="pagination-page"
          [class.active]="currentPage.toString() === page.title"
          [attr.aria-label]="'Đi đến trang ' + page.title"
          [attr.aria-current]="currentPage.toString() === page.title ? 'page' : null"
        >
          {{ page.title }}
        </a>
        
        <ng-template #ellipsis>
          <button 
            (click)="toggleSearch()" 
            class="pagination-ellipsis"
            type="button"
            title="Nhập số trang"
            aria-label="Mở ô tìm kiếm trang"
          >
            <span class="ellipsis-dots">⋯</span>
          </button>
        </ng-template>
      </ng-container>
    </div>

    <!-- Next Button -->
    <a
      *ngIf="currentPage < totalpage"
      [routerLink]="[rootLink, 'trang-' + (currentPage + 1)]"
      [queryParamsHandling]="'merge'"
      class="pagination-btn pagination-next"
      title="Trang tiếp"
      aria-label="Đi đến trang tiếp theo"
    >
      <span class="pagination-btn-text">Tiếp</span>
      <svg class="pagination-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="9,18 15,12 9,6"></polyline>
      </svg>
    </a>
  </nav>

  <!-- Quick page search -->
  <div *ngIf="showSearch" class="pagination-search" [@slideIn]>
    <div class="pagination-search-content">
      <label for="pageInput" class="pagination-search-label">Đi đến trang:</label>
      <div class="pagination-search-group">
        <input
          id="pageInput"
          type="number"
          #searchInput
          class="pagination-search-input"
          [placeholder]="currentPage.toString()"
          [min]="1"
          [max]="totalpage"
          (keyup.enter)="goToPage(searchInput.value); searchInput.value = ''"
          (keyup.escape)="showSearch = false"
          (input)="validateInput(searchInput)"
          autocomplete="off"
          aria-label="Nhập số trang muốn đi đến"
        />
        <button
          (click)="goToPage(searchInput.value); searchInput.value = ''"
          class="pagination-search-btn"
          type="button"
          title="Đi đến trang"
          [disabled]="!isValidPageInput(searchInput.value)"
        >
          <svg class="pagination-search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m9 18 6-6-6-6"/>
          </svg>
        </button>
      </div>
      <div class="pagination-search-info">
        <span class="text-xs text-neutral-500">
          Nhấn Enter để đi đến trang hoặc Esc để đóng
        </span>
      </div>
    </div>
  </div>
</div>
