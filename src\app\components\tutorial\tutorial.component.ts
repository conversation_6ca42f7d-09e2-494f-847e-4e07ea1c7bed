import { Component, ElementRef, EventEmitter, Output, ViewChild } from '@angular/core';

@Component({
  selector: 'app-tutorial',
  imports: [],
  templateUrl: './tutorial.component.html',
  styleUrl: './tutorial.component.scss'
})
export class TutorialComponent {
  isVisible = false;
  @Output() next = new EventEmitter<any>();
  @Output() back = new EventEmitter<any>();
  @ViewChild('tutorial') tutorial: ElementRef<HTMLElement> | undefined;
  position: { x: number; y: number } = { x: 0, y: 0 };
  public setPosition(offsetX: number, offsetY: number) {
    this.isVisible = true
    this.position = { x: offsetX, y: offsetY };
    // if (this.tutorial) {
    //   console.log(this.tutorial);
    //   this.
    //   this.tutorial.nativeElement.style.left = offsetX + 'px';
    //   this.tutorial.nativeElement.style.top = offsetY + 'px';
    // }
  }

  public hide() {
    this.isVisible = false
  }

}
