import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ViewEncapsulation, ChangeDetectorRef, Inject, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { isPlatformBrowser } from '@angular/common';
import { Subject, takeUntil, switchMap, map, catchError, of, BehaviorSubject } from 'rxjs';

import { SeoService } from '@services/seo.service';
import { NovelService } from '@services/novel.service';
import { Novel } from '@schemas/Novel';
import { Genre } from '@schemas/Genre';
import { NovelList } from '@schemas/NovelList';
import { IServiceResponse } from '@schemas/ResponseType';
import { NovelCardV1Component } from '@components/novel-card/card-v1/card-v1.component';
import { FaqComponent, FAQItem } from '@components/faq/faq.component';
import globalConfig from 'globalConfig';

interface GenrePageData {
  novels: Novel[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasMore: boolean;
}

@Component({
  selector: 'app-genre-page',
  templateUrl: './genre-page.component.html',
  styleUrls: ['./genre-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [
    CommonModule,
    RouterLink,
    NovelCardV1Component,
    FaqComponent
  ]
})
export class GenrePageComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private loadingSubject = new BehaviorSubject<boolean>(false);

  genre: Genre | null = null;
  genreData: GenrePageData = {
    novels: [],
    totalCount: 0,
    currentPage: 1,
    totalPages: 0,
    hasMore: false
  };

  // Filter options
  sortBy: string = 'update'; // update, view, rating, chapter
  status: string = 'all'; // all, ongoing, completed
  pageSize: number = 24;

  // Loading states
  loading$ = this.loadingSubject.asObservable();
  isLoadingMore = false;

  // SEO data
  breadcrumbLinks: Array<{label: string, url?: string}> = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private seoService: SeoService,
    private novelService: NovelService,
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit() {
    this.setupRouteSubscription();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupRouteSubscription() {
    this.route.params.pipe(
      takeUntil(this.destroy$),
      switchMap(params => {
        const genreSlug = params['slug'];
        if (!genreSlug) {
          this.router.navigate(['/']);
          return of(null);
        }

        this.loadingSubject.next(true);
        return this.loadGenreData(genreSlug);
      })
    ).subscribe({
      next: (result) => {
        if (result) {
          this.genre = result.genre;
          this.genreData = result.data;
          this.setupSEO();
          this.setupBreadcrumbs();
        }
        this.loadingSubject.next(false);
        this.cd.markForCheck();
      },
      error: (error) => {
        console.error('Error loading genre data:', error);
        this.loadingSubject.next(false);
        this.router.navigate(['/']);
      }
    });

    // Listen to query params for filters
    this.route.queryParams.pipe(
      takeUntil(this.destroy$)
    ).subscribe(params => {
      this.sortBy = params['sort'] || 'update';
      this.status = params['status'] || 'all';
      const page = parseInt(params['page']) || 1;

      if (this.genre && page !== this.genreData.currentPage) {
        this.loadPage(page);
      }
    });
  }

  private loadGenreData(genreSlug: string) {
    // Use the advanced search to get novels by genre
    return this.novelService.getAdvanceSearchNovel(
      1, // page
      this.pageSize, // step
      this.getSortType(this.sortBy), // sort
      this.getStatusType(this.status), // status
      genreSlug, // genres
      '', // nogenres
      0, // translation
      0, // wordcount
      '' // keyword
    ).pipe(
      map((response: IServiceResponse<NovelList>) => {
        if (!response.data) {
          throw new Error('Failed to load genre data');
        }

        // Create genre object from predefined genres or mock
        const genre = this.getGenreFromSlug(genreSlug);

        return {
          genre,
          data: {
            novels: response.data.novels || [],
            totalCount: (response.data.totalpage || 0) * this.pageSize, // Estimate total count
            currentPage: response.data.page || 1,
            totalPages: response.data.totalpage || 0,
            hasMore: (response.data.page || 1) < (response.data.totalpage || 0)
          }
        };
      }),
      catchError(error => {
        console.error('Error in loadGenreData:', error);
        return of(null);
      })
    );
  }

  private getSortType(sortBy: string): number {
    const sortMap: { [key: string]: number } = {
      'update': 0, // TopAll
      'view': 1,   // TopMonth
      'rating': 2, // TopWeek
      'chapter': 3 // TopDay
    };
    return sortMap[sortBy] || 0;
  }

  private getStatusType(status: string): number {
    const statusMap: { [key: string]: number } = {
      'all': 0,
      'ongoing': 1,
      'completed': 2
    };
    return statusMap[status] || 0;
  }

  private getGenreFromSlug(slug: string): Genre {
    // Try to find from predefined genres first
    const predefinedGenre = this.getPredefinedGenre(slug);
    if (predefinedGenre) {
      return predefinedGenre;
    }

    // Create mock genre object
    return {
      id: 1,
      title: this.getGenreTitle(slug),
      slug: slug,
      group: 1,
      description: this.getGenreDescription(slug)
    };
  }

  private getPredefinedGenre(slug: string): Genre | null {
    const genreMap: { [key: string]: Genre } = {
      'ngon-tinh': { id: 1, title: 'Ngôn Tình', slug: 'ngon-tinh', group: 1, description: 'Thể loại truyện tình cảm lãng mạn' },
      'tien-hiep': { id: 2, title: 'Tiên Hiệp', slug: 'tien-hiep', group: 2, description: 'Thể loại truyện tu tiên, tu ma' },
      'kiem-hiep': { id: 3, title: 'Kiếm Hiệp', slug: 'kiem-hiep', group: 2, description: 'Thể loại truyện võ hiệp cổ điển' },
      'do-thi': { id: 4, title: 'Đô Thị', slug: 'do-thi', group: 3, description: 'Thể loại truyện hiện đại, đô thị' },
      'huyen-huyen': { id: 5, title: 'Huyền Huyễn', slug: 'huyen-huyen', group: 2, description: 'Thể loại truyện kỳ ảo, ma pháp' },
      'xuyen-khong': { id: 6, title: 'Xuyên Không', slug: 'xuyen-khong', group: 1, description: 'Thể loại truyện du hành thời gian' }
    };
    return genreMap[slug] || null;
  }

  private getGenreTitle(slug: string): string {
    const genreTitles: { [key: string]: string } = {
      'ngon-tinh': 'Ngôn Tình',
      'tien-hiep': 'Tiên Hiệp',
      'kiem-hiep': 'Kiếm Hiệp',
      'do-thi': 'Đô Thị',
      'huyen-huyen': 'Huyền Huyễn',
      'xuyen-khong': 'Xuyên Không',
      'co-dai': 'Cổ Đại',
      'hien-dai': 'Hiện Đại',
      'quan-truong': 'Quan Trường',
      'hoc-duong': 'Học Đường'
    };
    return genreTitles[slug] || slug.charAt(0).toUpperCase() + slug.slice(1);
  }

  private getGenreDescription(slug: string): string {
    const descriptions: { [key: string]: string } = {
      'ngon-tinh': 'Thể loại truyện tình cảm lãng mạn với những câu chuyện tình yêu ngọt ngào',
      'tien-hiep': 'Thể loại truyện tu tiên, tu ma với thế giới huyền bí đầy phép thuật',
      'kiem-hiep': 'Thể loại truyện võ hiệp cổ điển với bối cảnh giang hồ võ lâm',
      'do-thi': 'Thể loại truyện với bối cảnh hiện đại, cuộc sống đô thị',
      'huyen-huyen': 'Thể loại truyện kỳ ảo với thế giới ma pháp và sinh vật huyền bí',
      'xuyen-khong': 'Thể loại truyện du hành thời gian, xuyên việt các thời đại'
    };
    return descriptions[slug] || `Thể loại ${this.getGenreTitle(slug)} với những câu chuyện hấp dẫn`;
  }

  private setupSEO() {
    if (!this.genre) return;

    this.seoService.setGenreSEO(this.genre, this.genreData.novels, this.genreData.currentPage);

    // Add structured data
    const schemas = [
      this.seoService.generateNovelListSchema(
        this.genreData.novels.slice(0, 10),
        `Truyện ${this.genre.title}`,
        `Danh sách truyện ${this.genre.title} hay nhất tại SayTruyenHot`
      ),
      this.generateGenrePageSchema()
    ];

    this.seoService.addStructuredData(schemas);
  }

  private generateGenrePageSchema(): any {
    if (!this.genre) return {};

    return {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      'name': `Truyện ${this.genre.title}`,
      'description': this.genre.description,
      'url': `${globalConfig.BASE_URL}/the-loai/${this.genre.slug}`,
      'mainEntity': {
        '@type': 'ItemList',
        'name': `Danh sách truyện ${this.genre.title}`,
        'numberOfItems': this.genreData.totalCount,
        'itemListElement': this.genreData.novels.slice(0, 10).map((novel, index) => ({
          '@type': 'ListItem',
          'position': index + 1,
          'item': {
            '@type': 'Book',
            'name': novel.title,
            'url': `${globalConfig.BASE_URL}/truyen/${novel.url}-${novel.id}`,
            'image': novel.coverImage,
            'author': novel.author,
            'genre': this.genre!.title
          }
        }))
      }
    };
  }

  private setupBreadcrumbs() {
    if (!this.genre) return;

    this.breadcrumbLinks = [
      { label: 'Trang chủ', url: '/' },
      { label: 'Thể loại', url: '/the-loai' },
      { label: this.genre.title }
    ];
  }

  // Pagination and filtering methods
  loadPage(page: number) {
    if (page === this.genreData.currentPage || !this.genre) return;

    this.loadingSubject.next(true);

    this.novelService.getAdvanceSearchNovel(
      page,
      this.pageSize,
      this.getSortType(this.sortBy),
      this.getStatusType(this.status),
      this.genre.slug,
      '',
      0,
      0,
      ''
    ).subscribe({
      next: (response: IServiceResponse<NovelList>) => {
        if (response.data) {
          this.genreData = {
            novels: response.data.novels || [],
            totalCount: (response.data.totalpage || 0) * this.pageSize,
            currentPage: response.data.page || page,
            totalPages: response.data.totalpage || 0,
            hasMore: (response.data.page || page) < (response.data.totalpage || 0)
          };

          // Update URL without reloading
          this.router.navigate([], {
            relativeTo: this.route,
            queryParams: { page: page > 1 ? page : null },
            queryParamsHandling: 'merge'
          });
        }
        this.loadingSubject.next(false);
        this.cd.markForCheck();
      },
      error: (error) => {
        console.error('Error loading page:', error);
        this.loadingSubject.next(false);
      }
    });
  }

  onSortChange(newSort: string) {
    if (newSort === this.sortBy) return;

    this.sortBy = newSort;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { sort: newSort, page: null },
      queryParamsHandling: 'merge'
    });
  }

  onStatusChange(newStatus: string) {
    if (newStatus === this.status) return;

    this.status = newStatus;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: { status: newStatus, page: null },
      queryParamsHandling: 'merge'
    });
  }

  // FAQ data for genre
  getGenreFAQs(): FAQItem[] {
    if (!this.genre) return [];

    return [
      {
        question: `Truyện ${this.genre.title} là gì?`,
        answer: `<strong>Truyện ${this.genre.title}</strong> là ${this.genre.description}. Tại SayTruyenHot, chúng tôi có <strong>${this.genreData.totalCount}+ bộ truyện ${this.genre.title}</strong> chất lượng cao, cập nhật liên tục.`,
        category: 'definition'
      },
      {
        question: `Có bao nhiêu truyện ${this.genre.title} tại SayTruyenHot?`,
        answer: `Hiện tại SayTruyenHot có <strong>${this.genreData.totalCount} bộ truyện ${this.genre.title}</strong> từ nhiều tác giả khác nhau, bao gồm cả truyện đã hoàn thành và đang cập nhật.`,
        category: 'collection'
      },
      {
        question: `Truyện ${this.genre.title} nào hay nhất?`,
        answer: `Những bộ truyện ${this.genre.title} được đánh giá cao nhất hiện đang được hiển thị trong danh sách. Bạn có thể sắp xếp theo <strong>đánh giá</strong> hoặc <strong>lượt xem</strong> để tìm những bộ truyện hay nhất.`,
        category: 'recommendations'
      },
      {
        question: `Có thể đọc truyện ${this.genre.title} miễn phí không?`,
        answer: `Có, tất cả truyện ${this.genre.title} tại SayTruyenHot đều <strong>hoàn toàn miễn phí</strong>. Bạn có thể đọc không giới hạn mà không cần đăng ký tài khoản.`,
        category: 'pricing'
      }
    ];
  }

  // Utility methods
  trackByNovel(_index: number, novel: Novel): number {
    return novel.id;
  }

  isPlatformBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  getPaginationPages(): (number | string)[] {
    const current = this.genreData.currentPage;
    const total = this.genreData.totalPages;
    const pages: (number | string)[] = [];

    if (total <= 7) {
      // Show all pages if total is small
      for (let i = 1; i <= total; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (current > 4) {
        pages.push('...');
      }

      // Show pages around current
      const start = Math.max(2, current - 1);
      const end = Math.min(total - 1, current + 1);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (current < total - 3) {
        pages.push('...');
      }

      // Always show last page
      if (total > 1) {
        pages.push(total);
      }
    }

    return pages;
  }
}
