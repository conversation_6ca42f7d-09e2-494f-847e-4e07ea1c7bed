<div class="chapter-panel">
  <span class="chapter-title w-full">
    <svg
      fill="currentColor"
      class="size-5"
      version="1.1"
      id="Capa_1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      width="64px"
      height="64px"
      viewBox="0 0 36.293 36.292"
      xml:space="preserve"
    >
      <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
      <g
        id="SVGRepo_tracerCarrier"
        stroke-linecap="round"
        stroke-linejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        <g>
          <path
            d="M7.984,15.654v4.985c0,0.827-0.672,1.5-1.5,1.5H1.5c-0.828,0-1.5-0.673-1.5-1.5v-4.985c0-0.829,0.672-1.5,1.5-1.5h4.984 C7.312,14.154,7.984,14.827,7.984,15.654z M34.792,14.154H13.289h-0.781c-0.827,0-1.5,0.671-1.5,1.5v4.985 c0,0.827,0.673,1.5,1.5,1.5h0.781h21.504c0.828,0,1.5-0.673,1.5-1.5v-4.985C36.292,14.827,35.621,14.154,34.792,14.154z M6.484,2.779H1.5c-0.828,0-1.5,0.671-1.5,1.5v4.985c0,0.829,0.672,1.5,1.5,1.5h4.984c0.828,0,1.5-0.671,1.5-1.5V4.279 C7.984,3.452,7.312,2.779,6.484,2.779z M34.792,2.779H13.289h-0.781c-0.827,0-1.5,0.671-1.5,1.5v4.985c0,0.829,0.673,1.5,1.5,1.5 h0.781h21.504c0.828,0,1.5-0.671,1.5-1.5V4.279C36.292,3.452,35.621,2.779,34.792,2.779z M6.484,25.53H1.5 c-0.828,0-1.5,0.671-1.5,1.5v4.984c0,0.83,0.672,1.5,1.5,1.5h4.984c0.828,0,1.5-0.67,1.5-1.5V27.03 C7.984,26.201,7.312,25.53,6.484,25.53z M34.792,25.53H13.289h-0.781c-0.827,0-1.5,0.671-1.5,1.5v4.984c0,0.83,0.673,1.5,1.5,1.5 h0.781h21.504c0.828,0,1.5-0.67,1.5-1.5V27.03C36.292,26.201,35.621,25.53,34.792,25.53z"
          ></path>
        </g>
      </g>
    </svg>
    <p class="chapter-title-text">Danh sách chương</p>
  </span>
  <div class="chapter-controls">
    <app-select
      [options]="options"
      [selectedValue]="currentPage"
      (selectedValueChange)="onPageChange($event)"
      size="small"
      class="w-32 rounded-lg text-sm"
    >
    </app-select>
    <!-- <button type="button" class="mx-2 p-1 rounded border hover:bg-gray-100 bg-white ">
      <svg class="size-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <g id="Edit / Sort_Descending"> <path id="Vector" d="M4 17H16M4 12H13M4 7H10M18 13V5M18 5L21 8M18 5L15 8" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> </g> </g></svg>
    </button> -->

    <label
      for="order"
      class="mx-2 p-0.5 rounded border border-gray-400 hover:bg-gray-100 bg-white cursor-pointer"
    >
      <input
        class="hidden peer/order"
        type="text"
        id="order"
        type="checkbox"
        [checked]="isIncrease"
        (change)="onOrderChange($event)"
      />
      <svg
        class="size-5 peer-checked/order:hidden"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        ></g>
        <g id="SVGRepo_iconCarrier">
          <g id="Edit / Sort_Descending">
            <path
              id="Vector"
              d="M4 17H16M4 12H13M4 7H10M18 13V5M18 5L21 8M18 5L15 8"
              stroke="#000000"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
          </g>
        </g>
      </svg>
      <svg
        class="size-5 hidden peer-checked/order:block"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        ></g>
        <g id="SVGRepo_iconCarrier">
          <g id="Edit / Sort_Ascending">
            <path
              id="Vector"
              d="M4 17H10M4 12H13M18 11V19M18 19L21 16M18 19L15 16M4 7H16"
              stroke="#000000"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></path>
          </g>
        </g>
      </svg>
    </label>
  </div>
</div>

<div class="relative mt-2 flex h-72 lg:h-[25rem]">
  <div class="flex flex-col gap-2 w-full overflow-y-scroll scrollbar-style-lg">
    <ng-container *ngFor="let chapter of allchapters; let i = index">
      <a
        class="chapter-item"
        [routerLink]="['/', novel?.url, 'chuong-' + chapter.slug!, chapter.id]"
        [title]="'Chương ' + chapter.slug"
      >
        <svg
          class="size-4 flex-shrink-0"
          viewBox="0 0 48 48"
          fill="#000000"
          stroke="currentColor"
          stroke-width="3.5"
        >
          <path
            stroke="currentColor"
            fill="none"
            d="M10.35,4.5a2,2,0,0,0-1.95,2v35.1a2,2,0,0,0,1.95,2h27.3a2,2,0,0,0,2-2V6.45a2,2,0,0,0-2-1.95h-2v8.82L31.79,9.41l-3.88,3.91V4.5Zm5.84,20H32.81M16.19,36.18H28.88M16.19,30.33h9.74"
          ></path>
        </svg>
        <p class="chapter-item-title">Chương {{ chapter.slug }}:</p>
        <p class="font-light">{{ chapter.title }}</p>
      </a>
    </ng-container>
  </div>
  <div *ngIf="isChapterLoading" class="chapter-loading">
    <app-spinner [sizeSpinner]="'60'"></app-spinner>
  </div>
</div>
