// @layer utilities {
    .cardv2-image
    {
        @apply shadow-md object-cover size-full rounded-md hover:brightness-75 hover:scale-105 transition-all duration-300 ease-in-out;
    }

    .cardv2-panel
    {
        @apply relative w-full flex bg-white shadow-[0px_1px_6px_1px_rgba(0,0,0,0.1)] dark:bg-dark-700 rounded-xl overflow-hidden ;
    }

    .cardv2-small
    {
        @apply h-32;

        .cardv2-link-image
        {
            @apply h-full min-w-24 p-2 overflow-hidden flex rounded-md;
        }
        .cardv2-title
        {
            @apply line-clamp-1 font-medium text-sm;
        }
        .cardv2-author
        {
            @apply line-clamp-1 font-normal text-xs text-gray-600 dark:text-dark-100;
        }
        .cardv2-description
        {
            @apply line-clamp-4 text-xs font-light mt-1;
        }
    }
    // .cardv2-medium
    // {
    //     @apply h-40;
    //     .cardv2-link-image
    //     {
    //         @apply h-full min-w-28 p-2 overflow-hidden flex;
    //     }
    //     .cardv2-title
    //     {
    //         @apply line-clamp-2 font-medium;
    //     }
    //     .cardv2-author
    //     {
    //         @apply line-clamp-1 font-normal text-sm text-gray-600 dark:text-dark-100;
    //     }
    //     .cardv2-description
    //     {
    //         @apply line-clamp-4 text-xs font-light mt-1;
    //     }
    // }


    .cardv2-image
    {
        @apply shadow-md object-cover size-full rounded-md hover:brightness-75 hover:scale-105 transition-all duration-300 ease-in-out;
    }
// }