import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-toggle',
  standalone: true,
  templateUrl: './toggle.component.html',
  styleUrl: './toggle.component.scss',
  imports: [CommonModule],
})
export class ToggleComponent {


  @Input()
  value: boolean = false;

  @Output()
  valueChange = new EventEmitter<boolean>();

  onToggleChange(event: Event) {

    const inputElement = event.target as HTMLInputElement;
    this.value = inputElement.checked; // Cập nhật trạng thái toggle
    this.valueChange.emit(this.value); // Phát ra sự kiện valueChange với giá trị mới
  }

}
