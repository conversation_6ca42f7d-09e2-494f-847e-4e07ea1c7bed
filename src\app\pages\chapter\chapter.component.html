<div class="common-container">
  <app-breadcrumb
    class="flex py-2"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: chapter?.novel?.title, url: '/truyen/' + chapter?.novel?.url + '-' + chapter?.novel?.id },
      { label: 'Chương ' + chapter?.slug, url: '/' + chapter?.novel?.url +  '/chuong-' + chapter?.slug + '/' + chapter?.id }
    ]"
  >
  </app-breadcrumb>
  <div
    [ngStyle]="{ 'background-image': 'url(' + texture + ')' }"
    [ngClass]="{
      'filter sepia-50': !!texture
    }"
    #chapterContainer
    class="flex-col rounded-md bg-neutral-100 dark:bg-dark-600 relative py-12 border dark:border-dark-550 overflow-hidden"
  >
    <div
      *ngIf="chapter"
      #toolbar
      class="text-black flex-center gap-5 w-full left-0 z-10"
      [ngClass]="{
        fixed: fixedToolbar,
        absolute: !fixedToolbar,
        'top-[4.5rem]': fixedToolbar && fixedHeader,
        'top-2': !fixedHeader || !fixedToolbar
      }"
    >
      <app-chapter-popup
        (appClickOutside)="toolbarState.isOpenChapterPopup = false"
        [elementIgnore]="ListChapterBtn"
        class="absolute w-full xs:w-[30rem] top-full left-1/2 -translate-x-1/2"
        [chapterPage]="chapter"
        [(visible)]="toolbarState.isOpenChapterPopup"
      />

      <div
        appTutorial
        [group]="'chapter'"
        class="mx-2 xs:gap-5 w-full xs:w-fit flex justify-between py-1 px-2 rounded-full bg-white dark:bg-dark-950/40 backdrop-blur-sm shadow-md"
      >
        <a
          [routerLink]="[
            '/',
            chapter!.novel.url,
            'chuong-' + chapter!.prevChapter?.slug,
            chapter!.prevChapter?.id
          ]"
          class="toolbar-button bg-neutral-600 text-white p-2"
          [title]="'Chương trước: ' + (chapter!.prevChapter?.title || 'Chương ' + chapter!.prevChapter?.slug)"
          [attr.aria-label]="'Đọc chương trước: ' + (chapter!.prevChapter?.title || 'Chương ' + chapter!.prevChapter?.slug)"
          rel="prev"
          [ngClass]="{
            'pointer-events-none !bg-neutral-200': chapter!.prevChapter == null
          }"
        >
          <svg
            class="h-6 w-6"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <line x1="5" y1="12" x2="19" y2="12" />
            <line x1="5" y1="12" x2="11" y2="18" />
            <line x1="5" y1="12" x2="11" y2="6" />
          </svg>
        </a>
        <button
          class="toolbar-button"
          [ngClass]="{ active: this.toolbarState.isScrollDown }"
          aria-label="scroll-auto"
          (click)="scrollSlowDown()"
        >
          <svg
            class="size-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g
              id="SVGRepo_tracerCarrier"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></g>
            <g id="SVGRepo_iconCarrier">
              <path
                d="M9.17154 16.8182L7.75732 18.2324L12 22.475L16.2426 18.2324L14.8284 16.8182L12 19.6466L9.17154 16.8182Z"
                fill="currentColor"
              ></path>
              <path
                d="M14.8284 7.182L16.2426 5.76779L12 1.52515L7.75733 5.76779L9.17155 7.182L12 4.35357L14.8284 7.182Z"
                fill="currentColor"
              ></path>
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M12 9.00018C13.6569 9.00018 15 10.3433 15 12.0002C15 13.657 13.6569 15.0002 12 15.0002C10.3431 15.0002 9 13.657 9 12.0002C9 10.3433 10.3431 9.00018 12 9.00018ZM12 11.0002C12.5523 11.0002 13 11.4479 13 12.0002C13 12.5525 12.5523 13.0002 12 13.0002C11.4477 13.0002 11 12.5525 11 12.0002C11 11.4479 11.4477 11.0002 12 11.0002Z"
                fill="currentColor"
              ></path>
            </g>
          </svg>
        </button>

        <button
          class="toolbar-button"
          [ngClass]="{ active: toolbarState.isFullScreen }"
          aria-label="full-screen"
          (click)="enterFullscreen()"
        >
          <svg
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            class="size-5 text-write"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
            ></path>
          </svg>
        </button>
        <!-- <app-speed-dial [items]="items" class="size-10 flex">
          <ng-template #itemTemplate let-item>
            <span [innerHTML]="item.icon | safeHtml"></span>
          </ng-template>
        </app-speed-dial> -->
        <button
          class="toolbar-button p-1"
          [ngClass]="{ active: toolbarState.isOpenChapterPopup }"
          (click)="toolbarState.isOpenChapterPopup = !toolbarState.isOpenChapterPopup"
          aria-label="list chapter"
          #ListChapterBtn
        >
          <svg
            class="size-8"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <line x1="9" y1="6" x2="20" y2="6" />
            <line x1="9" y1="12" x2="20" y2="12" />
            <line x1="9" y1="18" x2="20" y2="18" />
            <line x1="5" y1="6" x2="5" y2="6.01" />
            <line x1="5" y1="12" x2="5" y2="12.01" />
            <line x1="5" y1="18" x2="5" y2="18.01" />
          </svg>
        </button>
        <button
          class="toolbar-button"
          [ngClass]="{ active: toolbarState.isShowPlayer }"
          aria-label="paging"
          (click)="showPlayer()"
        >
          <svg
            class="size-6"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g
              id="SVGRepo_tracerCarrier"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></g>
            <g id="SVGRepo_iconCarrier">
              <path
                d="M6 9.85986V14.1499"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
              <path
                d="M9 8.42993V15.5699"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
              <path
                d="M12 7V17"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
              <path
                d="M15 8.42993V15.5699"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
              <path
                d="M18 9.85986V14.1499"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
              <path
                d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </g>
          </svg>
        </button>

        <button
          class="toolbar-button p-1"
          [ngClass]="{ active: toolbarState.isShowSetting }"
          #SettingBtn
          (click)="onOpenSetting()"
          aria-label="history"
        >
          <svg
            class="size-6"
            viewBox="0 0 39 39"
            fill="currentColor"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
          >
            <path
              d="M22.7935 3.09027C23.0422 3.09028 23.2845 3.16922 23.4855 3.31574C23.6865 3.46225 23.8358 3.66877 23.9119 3.90556L25.2041 7.92092C25.7469 8.18642 26.2661 8.48481 26.7619 8.82079L30.8877 7.93267C31.1309 7.88073 31.3844 7.90735 31.6116 8.00868C31.8387 8.11002 32.0279 8.28085 32.1517 8.49656L35.4082 14.1331C35.5325 14.3487 35.5852 14.5982 35.5586 14.8456C35.532 15.0931 35.4275 15.3257 35.2602 15.5099L32.429 18.6348C32.4702 19.234 32.4702 19.8353 32.429 20.4346L35.2602 23.5641C35.4275 23.7484 35.532 23.981 35.5586 24.2284C35.5852 24.4759 35.5325 24.7254 35.4082 24.941L32.1517 30.5799C32.0275 30.7951 31.8382 30.9655 31.6111 31.0664C31.384 31.1673 31.1307 31.1935 30.8877 31.1414L26.7619 30.2533C26.2685 30.5869 25.7469 30.8876 25.2065 31.1531L23.9119 35.1685C23.8358 35.4053 23.6865 35.6118 23.4855 35.7583C23.2845 35.9048 23.0422 35.9838 22.7935 35.9838H16.2806C16.0319 35.9838 15.7896 35.9048 15.5886 35.7583C15.3876 35.6118 15.2383 35.4053 15.1622 35.1685L13.8723 31.1555C13.3311 30.8908 12.809 30.5885 12.3099 30.2509L8.18645 31.1414C7.94319 31.1933 7.68972 31.1667 7.46256 31.0654C7.2354 30.964 7.04626 30.7932 6.9224 30.5775L3.66594 24.941C3.54159 24.7254 3.48892 24.4759 3.51552 24.2284C3.54212 23.981 3.64663 23.7484 3.81396 23.5641L6.64515 20.4346C6.60413 19.8369 6.60413 19.2372 6.64515 18.6395L3.81396 15.5099C3.64663 15.3257 3.54212 15.0931 3.51552 14.8456C3.48892 14.5982 3.54159 14.3487 3.66594 14.1331L6.9224 8.49421C7.0466 8.27893 7.23588 8.10859 7.46302 8.00769C7.69016 7.90678 7.94344 7.88052 8.18645 7.93267L12.3099 8.82314C12.808 8.48716 13.3296 8.18407 13.8723 7.91857L15.1646 3.90556C15.2404 3.66954 15.389 3.46356 15.5891 3.31712C15.7891 3.17068 16.0303 3.09127 16.2783 3.09027H22.7912H22.7935ZM21.9336 5.43981H17.1405L15.806 9.59144L14.9061 10.0308C14.4637 10.2473 14.0367 10.4938 13.628 10.7686L12.7962 11.3324L8.52948 10.4114L6.13295 14.5654L9.05813 17.8031L8.98764 18.7993C8.95387 19.2905 8.95387 19.7835 8.98764 20.2748L9.05813 21.271L6.12825 24.5087L8.52713 28.6626L12.7939 27.744L13.6256 28.3055C14.0343 28.5803 14.4614 28.8268 14.9038 29.0433L15.8036 29.4826L17.1405 33.6343H21.9383L23.2775 29.4803L24.1751 29.0433C24.617 28.8273 25.0433 28.5808 25.4509 28.3055L26.2802 27.744L30.5493 28.6626L32.9459 24.5087L30.0184 21.271L30.0888 20.2748C30.1227 19.7827 30.1227 19.289 30.0888 18.7969L30.0184 17.8007L32.9482 14.5654L30.5493 10.4114L26.2802 11.3277L25.4509 10.7686C25.0433 10.4933 24.617 10.2467 24.1751 10.0308L23.2775 9.59379L21.9359 5.43981H21.9336ZM19.5371 12.4884C21.4065 12.4884 23.1993 13.231 24.5212 14.5529C25.8431 15.8748 26.5857 17.6676 26.5857 19.537C26.5857 21.4064 25.8431 23.1993 24.5212 24.5212C23.1993 25.843 21.4065 26.5856 19.5371 26.5856C17.6677 26.5856 15.8748 25.843 14.5529 24.5212C13.2311 23.1993 12.4885 21.4064 12.4885 19.537C12.4885 17.6676 13.2311 15.8748 14.5529 14.5529C15.8748 13.231 17.6677 12.4884 19.5371 12.4884ZM19.5371 14.838C18.2908 14.838 17.0956 15.333 16.2143 16.2143C15.3331 17.0955 14.838 18.2908 14.838 19.537C14.838 20.7833 15.3331 21.9785 16.2143 22.8598C17.0956 23.741 18.2908 24.2361 19.5371 24.2361C20.7833 24.2361 21.9786 23.741 22.8598 22.8598C23.7411 21.9785 24.2361 20.7833 24.2361 19.537C24.2361 18.2908 23.7411 17.0955 22.8598 16.2143C21.9786 15.333 20.7833 14.838 19.5371 14.838Z"
            />
          </svg>
        </button>
        <a
          [routerLink]="[
            '/',
            chapter!.novel.url,
            'chuong-' + chapter!.nextChapter?.slug,
            chapter!.nextChapter?.id
          ]"
          class="toolbar-button bg-neutral-600 text-white p-2"
          [ngClass]="{
            'pointer-events-none !bg-neutral-200': chapter!.nextChapter === undefined
          }"
          [title]="'Chương tiếp: ' + (chapter!.nextChapter?.title || 'Chương ' + chapter!.nextChapter?.slug)"
          [attr.aria-label]="'Đọc chương tiếp: ' + (chapter!.nextChapter?.title || 'Chương ' + chapter!.nextChapter?.slug)"
          rel="next"
        >
          <svg
            class="h-6 w-6"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <line x1="5" y1="12" x2="19" y2="12" />
            <polyline points="12 5 19 12 12 19" />
          </svg>
        </a>
      </div>
    </div>
    <div
      class="flex flex-col size-full overflow-hidden dark:bg-dark-600 overflow-y-auto min-h-screen mt-14"
    >
    <h1 class="text-center p-2 mt-2 text-2xl text-primary-300 font-medium">
      {{ chapter?.novel?.title }}
    </h1>
      <h2 class="text-center text-gray-600 dark:text-gray-200">
        Chương {{ chapter?.slug }}: {{ chapter?.title }}
      </h2>
      <h3 class="text-center mt-2">- {{ chapter?.novel?.author }} -</h3>
      <ng-container *ngIf="chapter?.content; else loadingContent">
        <p
          class="p-5 md:p-28 !py-10 mt-5 lg:container mx-auto"
          [innerHTML]="chapter!.content"
          #content
        ></p>
      </ng-container>
      <ng-template #loadingContent>
        <div class="relative flex justify-center h-64">
          <app-spinner></app-spinner>
        </div>
      </ng-template>
    </div>
  </div>
  <div *ngIf="!isServer()" class="lg:container px-2 sm:px-1 mx-auto flex flex-col my-5">
    <span class="inline-flex items-center gap-2">
      <svg
        class="size-6"
        focusable="false"
        viewBox="0 0 24 24"
        data-testid="StarsIcon"
        fill="currentColor"
      >
        <path
          d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm4.24 16L12 15.45 7.77 18l1.12-4.81-3.73-3.23 4.92-.42L12 5l1.92 4.53 4.92.42-3.73 3.23L16.23 18z"
        ></path>
      </svg>
      <h2 class="text-xl font-semibold">TRUYỆN LIÊN QUAN</h2>
    </span>
    <div class="mt-4 grid grid-cols-3 md:grid-cols-6 gap-2 sm:gap-4">
      <ng-container
        *ngFor="let novel of similarNovels | slice : 0 : 6; index as i"
      >
        <app-card-v1 [novel]="similarNovels[i]"></app-card-v1>
      </ng-container>
    </div>
  </div>
</div>
