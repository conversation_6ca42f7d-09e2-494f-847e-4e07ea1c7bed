<div
  class="flex relative size-full "
  (appClickOutside)="this.isOpened = false"
>
  <button
    class="size-full transition-transform size-10 hover:bg-primary-50 items-center justify-center rounded-lg"
    (click)="toggleMenu()"
    [ngClass]="{ 'rotate-90': isOpened }"
    title="Speed Dial Menu"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      fill="currentColor"
      class="size-8 "
      version="1.1"
      id="Layer_1"
      viewBox="0 0 512 512"
      xml:space="preserve"
    >
      <g>
        <g>
          <path
            d="M437.019,74.98C388.667,26.628,324.381,0,256,0S123.333,26.628,74.982,74.98C26.628,123.333,0,187.62,0,256    s26.628,132.667,74.982,181.02C123.333,485.372,187.619,512,256,512s132.667-26.628,181.019-74.98    C485.372,388.667,512,324.38,512,256S485.372,123.333,437.019,74.98z M256,491.602c-129.911,0-235.602-105.69-235.602-235.602    S126.089,20.398,256,20.398S491.602,126.089,491.602,256S385.911,491.602,256,491.602z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            d="M382.477,247.846L201.951,112.217c-3.091-2.321-7.227-2.696-10.685-0.97c-3.456,1.727-5.641,5.26-5.641,9.124v271.258    c0,3.865,2.184,7.398,5.641,9.124c1.442,0.72,3.004,1.075,4.557,1.075c2.17,0,4.327-0.692,6.128-2.045l180.526-135.629    c2.564-1.927,4.073-4.948,4.073-8.154C386.55,252.793,385.041,249.772,382.477,247.846z M206.024,371.209V140.791L359.371,256    L206.024,371.209z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            d="M410.058,394.7c-3.996-3.971-10.452-3.951-14.425,0.045c-37.229,37.467-86.819,58.1-139.633,58.1    c-5.632,0-10.199,4.566-10.199,10.199c0,5.633,4.567,10.199,10.199,10.199c58.287,0,113.016-22.772,154.104-64.119    C414.074,405.128,414.053,398.671,410.058,394.7z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            d="M440.178,351.217c-4.868-2.832-11.111-1.183-13.945,3.684c-1.414,2.428-2.898,4.856-4.411,7.216    c-3.042,4.742-1.664,11.051,3.076,14.092c1.705,1.094,3.613,1.617,5.498,1.617c3.358,0,6.647-1.657,8.593-4.694    c1.673-2.607,3.313-5.288,4.873-7.97C446.697,360.294,445.046,354.05,440.178,351.217z"
          />
        </g>
      </g>
    </svg>
  </button>
  <div
    class="flex flex-col absolute w-full h-fit top-12 left-0 items-center transition-all gap-2"
    [ngClass]="{ invisible: !isOpened }"
  >
    <ng-container *ngFor="let item of items; let i = index">
      <button
        (click)="item.action(); buttonClick(i)"
        class="text-white w-10 h-10 scale-0 bg-primary-50 opacity-0 hover:bg-primary-100 rounded-full transition-all duration-100 flex-center"
        [ngClass]="{ 'opacity-100': isOpened, 'scale-100': isOpened }"
        [ngStyle]="{
          'transition-delay': i * 0.05 + 's'
        }"
      >
        <ng-template
          [ngTemplateOutlet]="itemTemplate || null"
          [ngTemplateOutletContext]="{ $implicit: item }"
        ></ng-template>
      </button>
    </ng-container>
  </div>
</div>
<ng-content />
