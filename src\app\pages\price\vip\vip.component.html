<div class="mt-10 flex flex-col items-center justify-center">
  <div class="mx-auto w-full max-w-[50rem]">
    <h2
      class="text-2xl mb-7 text-center text-title-sm font-bold text-black dark:text-white/90"
    >
      <PERSON><PERSON><PERSON> ký thành viên VIP để loại bỏ quảng cáo và nghe audio tại
      SayTruyenHot.com nhé!
    </h2>
  </div>

  <div>
    <div class="gird-cols-1 grid gap-5 sm:grid-cols-2 xl:grid-cols-3 xl:gap-6">
      <!-- Pricing item -->
      <div
        *ngFor="let item of items"
        class="rounded-2xl border border-gray-200 bg-white p-6 dark:border-gray-800 dark:bg-white/[0.03] shadow-common"
      >
        <span
          class="text-2xl mb-3 block text-theme-xl font-semibold text-gray-800 dark:text-white/90"
        >
          {{ item.name }}
        </span>

        <div class="mb-1 flex-between">
          <div class="flex items-end">
            <h2
              class="text-title-md font-bold text-gray-800 dark:text-white/90"
            >
              {{ item.price }}VNĐ
            </h2>

            <span
              class="mb-1 inline-block text-sm text-gray-500 dark:text-gray-400"
            >
              /tháng
            </span>
          </div>
          <span class="text-theme-xl font-semibold text-gray-400 line-through"
            >99.000VNĐ</span
          >
        </div>

        <p class="text-sm text-gray-500 dark:text-gray-400">
          Gói có thời hạn {{ item.quantity }} tháng kể từ ngày đăng ký
        </p>

        <div class="my-6 h-px w-full bg-gray-200 dark:bg-gray-800"></div>

        <div class="mb-8 space-y-3">
          <p
            class="flex-start gap-3 text-sm text-gray-500 dark:text-gray-400"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M13.4017 4.35986L6.12166 11.6399L2.59833 8.11657"
                stroke="#12B76A"
                stroke-width="1.8"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
            Nghe và đọc thoải mái
          </p>

          <p
            class="flex-start gap-3 text-sm text-gray-500 dark:text-gray-400"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M13.4017 4.35986L6.12166 11.6399L2.59833 8.11657"
                stroke="#12B76A"
                stroke-width="1.8"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
            Sở hữu thêm 02 giọng VIP khi nghe
          </p>

          <p
            class="flex-start gap-3 text-sm text-gray-500 dark:text-gray-400"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M13.4017 4.35986L6.12166 11.6399L2.59833 8.11657"
                stroke="#12B76A"
                stroke-width="1.8"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
            Loại bỏ quảng cáo
          </p>
        </div>

        <button
          (click)="buyVip(item)"
          class="flex w-full items-center justify-center rounded-lg bg-gray-800 p-3.5 text-sm font-medium text-white shadow-theme-xs transition-colors hover:bg-brand-500 dark:bg-white/10"
          aria-label="Mua ngay"
          >
          Chọn mua
        </button>
      </div>

      <!-- Pricing item -->
    </div>
  </div>
</div>
