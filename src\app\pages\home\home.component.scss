.swiper-mark-bg {
    // background: url('your-image.jpg') no-repeat center/cover;
  
    /* <PERSON><PERSON> dụng ảnh làm mask */
    -webkit-mask-image: url('/Subtract.png');
    mask-image: url('/Subtract.png');
  
    /* <PERSON><PERSON><PERSON><PERSON> chỉnh kích thước mask */
    -webkit-mask-size: auto 100%; /* Giữ chiều cao 100%, chiều rộng tự động */
    mask-size: auto 100%;
  
    /* Căn góc trên bên phải */
    -webkit-mask-position: right top;
    mask-position: right top;
  
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;

    @apply absolute size-full bg-gray-300/10 text-white dark:bg-dark-600/20 backdrop-blur-md rounded-3xl;
}

.custom-prev-button, .custom-next-button {
    @apply bg-gray-800 text-white border-none  cursor-pointer;
}

.caraousel-container {
    @apply flex bg-gray-200 h-[24rem] sm:h-[28rem] xl:h-[26rem] relative
}

.main-container {
    @apply mt-4 lg:mt-6 lg:container mx-auto space-y-4 lg:space-y-6 p-2;
}
.swiper-container {
    @apply flex bg-transparent h-[24rem] sm:h-[28rem] xl:h-[26rem] relative overflow-hidden;
}

.swiper-main-content {
    @apply lg:container relative pb-12 pt-6 xl:py-6 px-1 h-full rounded-lg flex items-end w-full mx-auto;
}
.swiper-novel-title {
    @apply text-xl sm:text-2xl md:text-3xl font-bold uppercase line-clamp-2;
}
.swiper-novel-link {
    @apply mt-auto mb-2 sm:mb-2 flex flex-col text-white pl-[0.5rem] sm:pl-[12.5rem] md:pl-[14rem] gap-1 text-center sm:text-left;
}
.swiper-novel-description {
    @apply text-sm line-clamp-5 mt-2 text-white;
}
.swiper-btn {
    @apply p-1 pointer-events-auto rounded relative flex-start px-3 overflow-hidden bg-gray-300/10 hover:bg-gray-300/50;
}
.swiper-btn-icon {
    @apply flex relative items-center justify-center font-medium select-none w-full pointer-events-none;
}
.swiper-btn-container {
    @apply absolute bottom-0 right-1/2 translate-x-1/2 xl:translate-x-0 xl:bottom-4 xl:right-0 h-12 flex justify-end items-center gap-4 mx-auto text-white;
}
.swiper-btn-text {
    @apply uppercase text-sm font-extrabold italic;
}
.bg-comic-image{
    @apply absolute inset-0 size-full backdrop-blur-md;
    background: radial-gradient(
    ellipse at center,
    rgba(0, 0, 0, 0.3) 0%,    /* phần giữa trong suốt */
    rgba(0, 0, 0, 0.3) 50%,    /* phần giữa trong suốt */
    rgba(0, 0, 0, 0.8) 100%  /* 4 góc mờ đen */
  );
}

.bg-comic-image::after {
    content: "";
    position: absolute;
    bottom: 0px;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    // background: linear-gradient(0deg, #000000 0%, #ffffff2b 5%, #ffffff00 80%, #3c3c3c 100%);
    @apply bg-gradient-to-t from-[var(--color-background-100)] dark:from-dark-background to-[10px] dark:to-[15px] to-transparent;
}

.image-container {
    transform: translateZ(50px);
    transform: perspective(500px) rotateY(-40deg);
}

.go-to-novel
{
    @apply absolute z-50 right-0 top-0 transform -translate-y-[20%] bg-gradient-to-r from-red-400 via-red-500 to-red-600 hover:bg-gradient-to-br focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 shadow-common shadow-red-500/50 dark:shadow-common dark:shadow-red-800/80 font-medium text-sm px-5 py-2.5 text-center rounded-full size-14 sm:size-16 flex-center;
}