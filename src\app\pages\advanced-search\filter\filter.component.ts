import { isPlatformBrowser } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  PLATFORM_ID,
} from '@angular/core';
import { Genre } from '@schemas/Genre';
import { NovelService } from '@services/novel.service';
import {
  IFilterOptions,
  IFilters,
  advancedFiltersOptions
} from '@components/utils/constants';

@Component({
  selector: 'app-filter',
  standalone: false,
  templateUrl: './filter.component.html',
  styleUrl: './filter.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FilterComponent implements OnInit {
  genresVisible = false;
  selectedCategory: string = '';
  dataView: IFilters;
  @Input()
  selectedValues: any
  statusGenres: { [key: string]: number } = {};
  @Output() onSubmitSearch: EventEmitter<any> = new EventEmitter();

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private novelService: NovelService,
    private cd: ChangeDetectorRef
  ) {
    this.dataView = {
      status: advancedFiltersOptions.status,
      sorts: advancedFiltersOptions.sorts,
      translations: advancedFiltersOptions.translations,
      wordcounts: advancedFiltersOptions.wordcounts,
      genres: [],
    };


  }
  ngOnInit(): void {

    if (!isPlatformBrowser(this.platformId)) return;
    if (this.selectedValues.genres) {
      let genres = this.selectedValues.genres.split(',').map((g: string) => this.novelService.getGenreById(+g));

      genres.forEach((g: Genre) => {
        this.statusGenres[g.id] = 1;
        this.dataView.genres.push({
          label: g.title,
          value: g.id,
          select: this.statusGenres[g.id],
        });
      })
    }
    if (this.selectedValues.nogenres) {
      let nogenres = this.selectedValues.nogenres.split(',').map((g: string) => this.novelService.getGenreById(+g));

      nogenres.forEach((g: Genre) => {
        this.statusGenres[g.id] = 2;
        this.dataView.genres.push({
          label: g.title,
          value: g.id,
          select: this.statusGenres[g.id],
        });
      })
    }


  }
  selectCategory(category: string) {
    this.selectedCategory = category;
  }

  getGenreKeys() {
    return this.dataView.genres.filter(genre => genre.select! > 0);
  }

  removeGenre(value: number | string) {
    this.dataView.genres = this.dataView.genres.filter(
      (g) => g.value !== value
    );
    this.statusGenres[value] = 0;
  }
  private updateGenres(): Promise<{ selectOptions: IFilterOptions }> {
    return new Promise((resolve) => {
      const genres: string[] = [];
      const nogenres: string[] = [];

      Object.entries(this.statusGenres).forEach(
        ([key, value]) => {
          if (value === 1) genres.push(key);
          if (value === 2) nogenres.push(key);
        },
      );
      this.selectedValues.genres = genres.join(',');
      this.selectedValues.nogenres = nogenres.join(',');
      resolve({
        selectOptions: this.selectedValues
      });
    });
  }

  async onSearch() {
    const data = await this.updateGenres();
    this.onSubmitSearch.emit(data.selectOptions);
    this.cd.detectChanges();
  }

  OnGenresChange(genre: Genre) {
    if (this.statusGenres[genre.id] >= 0)
      this.statusGenres[genre.id] = (this.statusGenres[genre.id] + 1) % 3
    else
      this.statusGenres[genre.id] = 1;

    const existing = this.dataView.genres.find(g => g.value === genre.id);
    if (existing) {
      // Nếu đã có, chỉ cập nhật select
      existing.select = this.statusGenres[genre.id];
    } else {
      // Nếu chưa có, push mới
      this.dataView.genres.push({
        label: genre.title,
        value: genre.id,
        select: this.statusGenres[genre.id],
      });
    }
    this.cd.detectChanges();


  }

  onSelectGenre(genre: Genre) {
    if (!genre.slug) {
      return;
    }
    this.OnGenresChange(genre);
  }
  onStatusGenres() {
    console.log(24234234);


  }
}
