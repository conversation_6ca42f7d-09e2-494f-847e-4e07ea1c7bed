.related-content {
  @apply space-y-12;
}

.related-section {
  @apply bg-white dark:bg-dark-700 rounded-lg p-6 shadow-sm;
  
  &.has-novels {
    @apply border border-gray-200 dark:border-dark-600;
  }
}

.section-header {
  @apply mb-6;
}

.section-title {
  @apply text-xl lg:text-2xl font-bold text-gray-900 dark:text-white mb-2;
}

.section-description {
  @apply text-gray-600 dark:text-gray-300 text-sm lg:text-base;
}

.novels-container {
  @apply space-y-6;
}

.novels-grid {
  @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4;
}

.novel-item {
  @apply transition-transform duration-200 hover:scale-105;
}

.section-footer {
  @apply text-center pt-4 border-t border-gray-200 dark:border-dark-600;
}

.view-all-link {
  @apply inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-semibold transition-colors duration-200;
  
  &:hover {
    @apply underline;
  }
}

.empty-section {
  @apply text-center py-8;
}

.empty-message {
  @apply text-gray-500 dark:text-gray-400 italic;
}

.no-related-content {
  @apply bg-gray-50 dark:bg-dark-800 rounded-lg p-8 text-center;
}

.no-content-message {
  @apply space-y-4;
  
  h3 {
    @apply text-lg font-semibold text-gray-900 dark:text-white;
  }
  
  p {
    @apply text-gray-600 dark:text-gray-300;
  }
}

.explore-link {
  @apply inline-block bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg transition-colors duration-200;
}

// Responsive Design
@media (max-width: 640px) {
  .related-section {
    @apply p-4;
  }
  
  .novels-grid {
    @apply grid-cols-2 gap-3;
  }
  
  .section-title {
    @apply text-lg;
  }
  
  .section-description {
    @apply text-sm;
  }
}
