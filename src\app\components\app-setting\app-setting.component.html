<app-dialog [(isVisible)]="isVisible">
  <div
    class="flex-start flex-col size-full sm:w-[40rem] md:w-[45rem] min-h-[34rem] bg-white dark:bg-neutral-800 dark:text-white sm:rounded-lg"
  >
    <span
      class="flex-center flex-col w-full p-4 border-common mb-2"
    >
      <svg
        class="size-6"
        viewBox="0 0 28 28"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M16.4253 1.75C16.6106 1.75001 16.791 1.80881 16.9407 1.91793C17.0904 2.02706 17.2016 2.18089 17.2583 2.35725L18.2208 5.348C18.6251 5.54575 19.0118 5.768 19.3811 6.01825L22.4541 5.35675C22.6353 5.31807 22.824 5.33789 22.9932 5.41337C23.1624 5.48885 23.3033 5.61608 23.3956 5.77675L25.8211 9.975C25.9137 10.1356 25.9529 10.3214 25.9331 10.5057C25.9133 10.69 25.8355 10.8633 25.7108 11.0005L23.6021 13.328C23.6328 13.7743 23.6328 14.2222 23.6021 14.6685L25.7108 16.9995C25.8355 17.1367 25.9133 17.31 25.9331 17.4943C25.9529 17.6786 25.9137 17.8644 25.8211 18.025L23.3956 22.225C23.3031 22.3853 23.1621 22.5122 22.9929 22.5874C22.8237 22.6625 22.6351 22.6821 22.4541 22.6432L19.3811 21.9818C19.0136 22.2302 18.6251 22.4543 18.2226 22.652L17.2583 25.6427C17.2016 25.8191 17.0904 25.9729 16.9407 26.0821C16.791 26.1912 16.6106 26.25 16.4253 26.25H11.5743C11.3891 26.25 11.2086 26.1912 11.0589 26.0821C10.9092 25.9729 10.798 25.8191 10.7413 25.6427L9.78057 22.6537C9.37742 22.4566 8.98856 22.2314 8.61682 21.98L5.54557 22.6432C5.36439 22.6819 5.1756 22.6621 5.0064 22.5866C4.8372 22.5112 4.69633 22.3839 4.60407 22.2233L2.17857 18.025C2.08595 17.8644 2.04672 17.6786 2.06654 17.4943C2.08635 17.31 2.16419 17.1367 2.28882 16.9995L4.39757 14.6685C4.36702 14.2234 4.36702 13.7766 4.39757 13.3315L2.28882 11.0005C2.16419 10.8633 2.08635 10.69 2.06654 10.5057C2.04672 10.3214 2.08595 10.1356 2.17857 9.975L4.60407 5.775C4.69658 5.61465 4.83757 5.48778 5.00674 5.41263C5.17592 5.33747 5.36457 5.31791 5.54557 5.35675L8.61682 6.02C8.98782 5.76975 9.37632 5.544 9.78057 5.34625L10.7431 2.35725C10.7996 2.18145 10.9102 2.02804 11.0592 1.91896C11.2082 1.80989 11.3879 1.75074 11.5726 1.75H16.4236H16.4253ZM15.7848 3.5H12.2148L11.2208 6.59225L10.5506 6.9195C10.2211 7.08073 9.903 7.26433 9.59857 7.469L8.97907 7.889L5.80107 7.203L4.01607 10.297L6.19482 12.7085L6.14232 13.4505C6.11717 13.8164 6.11717 14.1836 6.14232 14.5495L6.19482 15.2915L4.01257 17.703L5.79932 20.797L8.97732 20.1128L9.59682 20.531C9.90125 20.7357 10.2193 20.9193 10.5488 21.0805L11.2191 21.4077L12.2148 24.5H15.7883L16.7858 21.406L17.4543 21.0805C17.7835 20.9197 18.101 20.736 18.4046 20.531L19.0223 20.1128L22.2021 20.797L23.9871 17.703L21.8066 15.2915L21.8591 14.5495C21.8843 14.183 21.8843 13.8152 21.8591 13.4487L21.8066 12.7067L23.9888 10.297L22.2021 7.203L19.0223 7.8855L18.4046 7.469C18.101 7.26395 17.7835 7.08034 17.4543 6.9195L16.7858 6.594L15.7866 3.5H15.7848ZM13.9998 8.75C15.3922 8.75 16.7276 9.30312 17.7121 10.2877C18.6967 11.2723 19.2498 12.6076 19.2498 14C19.2498 15.3924 18.6967 16.7277 17.7121 17.7123C16.7276 18.6969 15.3922 19.25 13.9998 19.25C12.6074 19.25 11.2721 18.6969 10.2875 17.7123C9.30295 16.7277 8.74982 15.3924 8.74982 14C8.74982 12.6076 9.30295 11.2723 10.2875 10.2877C11.2721 9.30312 12.6074 8.75 13.9998 8.75ZM13.9998 10.5C13.0716 10.5 12.1813 10.8687 11.5249 11.5251C10.8686 12.1815 10.4998 13.0717 10.4998 14C10.4998 14.9283 10.8686 15.8185 11.5249 16.4749C12.1813 17.1313 13.0716 17.5 13.9998 17.5C14.9281 17.5 15.8183 17.1313 16.4747 16.4749C17.1311 15.8185 17.4998 14.9283 17.4998 14C17.4998 13.0717 17.1311 12.1815 16.4747 11.5251C15.8183 10.8687 14.9281 10.5 13.9998 10.5Z"
          fill="currentColor"
        />
      </svg>

      <h2 class="text-lg font-semibold">Cài đặt người dùng</h2>
    </span>

    <div class="size-full flex flex-col sm:flex-row bg-white dark:bg-dark-700">
      <div
        class="w-full sm:w-48 p-2 gap-2 grid grid-cols-2 sm:grid-cols-1 h-fit  justify-center sm:border-r-[1px] dark:border-dark-500"
      >
        <button
          (click)="selectOption(option)"
          *ngFor="let option of GroupSetting"
          class="h-10 bg-neutral-100 hover:bg-neutral-200 dark:bg-dark-600 hover:dark:bg-dark-700 rounded flex-start gap-2 font-normal"
          [ngClass]="{
            'bg-primary-100 hover:bg-primary-200 dark:bg-primary-100 text-white': option.value === selectedGroup
          }"
          [title]="option.label"
        > 
        <div class="mx-5 flex-start gap-3">

          <span [innerHTML]="option.icon! | safeHtml"></span>

          {{ option.label }}
        </div>
        </button>
      </div>

      <div class="flex-1 w-full p-2 ">
        <ng-container
          *ngFor="let setting of GroupSetting[this.selectedGroup - 1].settings"
       
        >
          <div class="flex justify-between w-full h-fit border-b-[1px] dark:border-dark-500 px-4 py-3">
            <div >
              <span> {{ setting.name }}</span>
              <p class=" text-xs text-neutral-500 dark:text-neutral-400">{{ setting.description }}</p>

            </div>
            <app-color-select
              *ngIf="setting.inputType === 5"
              [value]="setting.value"
              [name]="setting.name!"
              [options]="setting.options!"
              (valueChange)="OnSettingChange(setting, $event)"
            ></app-color-select>
            <app-select
              *ngIf="setting.inputType === 1"
              [options]="setting.options!"
              [selectedValue]="setting.value"
              (selectedValueChange)="OnSettingChange(setting, $event)"
              class="w-48"
              [size]="'small'"
            ></app-select>
            <app-toggle
              *ngIf="setting.inputType === 6"
              class="w-10 h-5"
              [value]="setting.value"
              (valueChange)="OnSettingChange(setting, $event)"
            >
            </app-toggle>
            <app-slider
              *ngIf="setting.inputType === 4"
              [value]="setting.value"
              (valueChange)="OnSettingChange(setting, $event)"
              [min]="setting.min!"
              [max]="setting.max!"
              [step]="setting.step!"
              class="w-48"
            >
            </app-slider>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</app-dialog>
