.custom-transition {
    @apply w-3/4 rounded pl-4 pr-8 py-2  outline-none transition-[width];

}

.search-icon {
    @apply size-8 text-white dark:text-primary-200 p-1.5;
}

.clear-icon {
    @apply opacity-100 duration-500;
}
.btn-search
{ @apply h-full flex bg-neutral-200/40 rounded-full cursor-pointer md:hidden dark:bg-dark-800 items-center justify-center backdrop-blur-sm; 
} 
.search-frame
{ @apply hidden absolute top-0 right-0 left-0 h-16 md:relative md:flex justify-end md:h-8 md:w-[32rem]; 
} 
.search-input
{ @apply border dark:border-neutral-500 dark:bg-neutral-700 bg-white/70 backdrop:blur-md border-none focus:bg-white dark:focus:bg-dark-600 text-black  absolute right-0 h-8 dark:text-white rounded-lg; 
} 
.search-result-panel
{ @apply transition-[width] absolute  z-[20] flex mt-20 md:mt-10 flex-col bg-white dark:bg-neutral-800 px-4 py-3 shadow-xl; 
} 
.item-search-skeleton
{ @apply rounded-lg bg-gray-100 dark:bg-neutral-700 my-2 hover:bg-gray-100 animate-pulse; 
} 
.item-search-result
{ @apply rounded-lg bg-gray-100 dark:bg-neutral-700 mb-2 hover:bg-gray-200 dark:hover:bg-neutral-600 dark:text-white; 
} 

