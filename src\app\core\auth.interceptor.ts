import { isPlatformBrowser, isPlatformServer } from "@angular/common";
import { <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpResponse } from "@angular/common/http";
import { Inject, Injectable, PLATFORM_ID, REQUEST } from "@angular/core";
import { AccountService } from "@services/account.service";
import { LoadingService } from "@services/loading.service";
import globalConfig from "globalConfig";
import { catchError, map, throwError } from "rxjs";

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

    constructor(private auth: AccountService, private loadingService: LoadingService,
        @Inject(PLATFORM_ID) private platformId: Object,
        @Inject(REQUEST) private request: Request

    ) { }

    intercept(req: HttpRequest<any>, next: HttpHandler) {
        // Get the auth token from the service.
        const authToken = this.auth.getAuthorizationToken();
        if (authToken) {
            req = req.clone({
                headers: req.headers.set('Authorization', `Bear<PERSON> ${authToken}`),
            });
        }
        if (isPlatformServer(this.platformId)) {
            let host = globalConfig.IS_PRODUCTION ? (this.request.headers.get('host') || globalConfig.HOST) : globalConfig.HOST;
            req = req.clone({
                headers: req.headers.set('X-Forwarded-Host', host)
            });
        }

        if (isPlatformBrowser(this.platformId)) {
            if (req.method === 'GET') {
                this.loadingService.TaskAdd(req.url);
            }

            return next
                .handle(req)
                .pipe(
                    catchError(err => {
                        if (req.method === 'GET') {
                            this.loadingService.TaskRemove(req.url);
                        }
                        return throwError(() => err);
                    }),

                    map(event => {
                        if (event instanceof HttpResponse) {
                            if (req.method === 'GET') {
                                this.loadingService.TaskRemove(req.url);
                            }
                        }
                        return event;
                    })
                )
        }

        return next.handle(req);

    }
}