{"name": "novels-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --no-hmr --port 4200", "publish": "ng serve --no-hmr --disable-host-check --host 0.0.0.0", "build": "ng build --configuration production && node sitemap/migrate.js", "watch": "ng build --watch --configuration development", "test": "ng test", "ssr": "node dist/novels-app/server/server.mjs", "sitemap": "node sitemap/sitemapGenerator.js && node sitemap/migrate.js"}, "private": true, "dependencies": {"@abacritt/angularx-social-login": "^2.3.0", "@angular/animations": "^19.2.6", "@angular/common": "^19.2.6", "@angular/compiler": "^19.2.6", "@angular/core": "^19.2.6", "@angular/forms": "^19.2.6", "@angular/platform-browser": "^19.2.6", "@angular/platform-browser-dynamic": "^19.2.6", "@angular/platform-server": "^19.2.6", "@angular/router": "^19.2.6", "@angular/ssr": "^19.2.7", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/postcss": "^4.0.4", "compression": "^1.8.0", "dotenv": "^17.2.1", "express": "^4.18.2", "ngx-cookie-service-ssr": "^19.1.2", "pg": "^8.16.3", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xmlbuilder2": "^3.1.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.7", "@angular/cli": "^19.2.7", "@angular/compiler-cli": "^19.2.6", "@types/compression": "^1.7.5", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "~5.6.2"}}