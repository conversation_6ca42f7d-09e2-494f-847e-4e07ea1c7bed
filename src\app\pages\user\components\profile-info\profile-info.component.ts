import { Component, Input, OnInit } from '@angular/core';
import { User } from '@schemas/User';
import { AccountService } from '@services/account.service';
import { ToastService, ToastType } from '@services/toast.service';
import { first } from 'rxjs';

@Component({
  selector: 'app-profile-info',
  templateUrl: './profile-info.component.html',
  styleUrls: ['./profile-info.component.scss'],
  standalone: false
})
export class ProfileInfoComponent implements OnInit {
  @Input() user!: User;
  
  showUpdateForm = false;
  defaultAvatar = '/default_avatar.jpeg';

  constructor(
    private accountService: AccountService,
    private toast: ToastService
  ) {}

  ngOnInit(): void {}

  onFileChange(event: any) {
    if (event.target.files && event.target.files.length) {
      const reader = new FileReader();
      if (event.target.files && event.target.files[0]) {
        const file = event.target.files[0];

        const avatar: FormData = new FormData();
        avatar.append('image', file, file.name);
        this.accountService
          .UpdateAvatar(avatar)
          .pipe(first())
          .subscribe((res: any) => {
            if (res.status == 200) {
              this.user.avatar = res.data;
              
              // Update stored user data
              const updatedUser = { ...this.user };
              updatedUser.token = this.accountService.getAuthorizationToken();
              this.accountService.SaveUser(updatedUser);

              this.toast.show(ToastType.Success, res.message);
            } else {
              this.toast.show(ToastType.Error, res.message);
            }
          });
      }
    }
  }

  toggleUpdateForm() {
    this.showUpdateForm = !this.showUpdateForm;
  }

  transform(dateString: string | undefined, FORMAT_DATE = 'DD/MM/YYYY'): string {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return '';
    }

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }
}
