<div class="bookcase-container">
  <!-- Header -->
  <div class="bookcase-header">
    <h2 class="bookcase-title">T<PERSON> sách của tôi</h2>
    <p class="bookcase-subtitle"><PERSON><PERSON><PERSON><PERSON> lý truyện yêu thích và lịch sử đọc</p>
  </div>

  <!-- Tab Navigation -->
  <div class="tab-navigation">
    <button 
      class="tab-button"
      [class.active]="activeTab === 'favorites'"
      (click)="switchTab('favorites')"
    >
      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
      </svg>
      <PERSON><PERSON><PERSON> th<PERSON>ch ({{ favoriteNovels.length }})
    </button>
    <button 
      class="tab-button"
      [class.active]="activeTab === 'history'"
      (click)="switchTab('history')"
    >
      <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
      </svg>
      Lịch sử ({{ readingHistory.length }})
    </button>
  </div>

  <!-- Tab Content -->
  <div class="tab-content">
    <!-- Favorites Tab -->
    <div *ngIf="activeTab === 'favorites'" class="favorites-tab">
      <div *ngIf="favoriteNovels.length === 0" class="empty-state">
        <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
        </svg>
        <h3>Chưa có truyện yêu thích</h3>
        <p>Hãy thêm những truyện bạn yêu thích để dễ dàng theo dõi!</p>
      </div>

      <div *ngIf="favoriteNovels.length > 0" class="novel-grid">
        <div *ngFor="let novel of favoriteNovels" class="novel-card">
          <div class="novel-cover">
            <img [src]="novel.cover" [alt]="novel.title" loading="lazy" />
            <div class="novel-actions">
              <button 
                class="action-btn remove-btn"
                (click)="removeFromFavorites(novel.id)"
                title="Xóa khỏi yêu thích"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>
          </div>
          
          <div class="novel-info">
            <h3 class="novel-title">{{ novel.title }}</h3>
            <p class="novel-author">{{ novel.author }}</p>
            
            <div class="reading-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  [style.width]="getProgressWidth(novel.progress)"
                ></div>
              </div>
              <span class="progress-text">
                {{ novel.currentChapter }}/{{ novel.totalChapters }} chương
              </span>
            </div>
            
            <p class="last-read">Đọc lần cuối: {{ novel.lastRead }}</p>
            
            <button class="continue-btn">Tiếp tục đọc</button>
          </div>
        </div>
      </div>
    </div>

    <!-- History Tab -->
    <div *ngIf="activeTab === 'history'" class="history-tab">
      <div class="history-actions">
        <button 
          class="clear-history-btn"
          (click)="clearHistory()"
          [disabled]="readingHistory.length === 0"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          Xóa tất cả lịch sử
        </button>
      </div>

      <div *ngIf="readingHistory.length === 0" class="empty-state">
        <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <h3>Chưa có lịch sử đọc</h3>
        <p>Lịch sử đọc truyện của bạn sẽ hiển thị tại đây</p>
      </div>

      <div *ngIf="readingHistory.length > 0" class="history-list">
        <div *ngFor="let item of readingHistory" class="history-item">
          <div class="history-cover">
            <img [src]="item.cover" [alt]="item.title" loading="lazy" />
          </div>
          
          <div class="history-info">
            <h4 class="history-title">{{ item.title }}</h4>
            <p class="history-author">{{ item.author }}</p>
            <p class="history-chapter">{{ item.chapter }}</p>
            <p class="history-time">{{ item.lastRead }}</p>
          </div>
          
          <div class="history-actions">
            <button class="continue-btn small">Tiếp tục</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
