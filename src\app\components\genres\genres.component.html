<div *ngIf="visible" class="@container/main genre-container origin-top" [@fadeInOut]>
  <form class="genre-search">
    <div class="genre-search-icon">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        fill="none"
        viewBox="0 0 24 24"
        class="feather feather-info icon small text-icon-contrast text-undefined"
      >
        <path
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M11 19a8 8 0 1 0 0-16 8 8 0 0 0 0 16m10 2-4.35-4.35"
        ></path>
      </svg>
    </div>
    <input
      class="genre-input"
      type="search"
      placeholder="Tìm thể loại..."
      title="Search"
      name="search"
      max="100"
      (input)="filterGenres(searchQuery.value)"
      #searchQuery
    />
  </form>

  <div class="genre-hover-info">
    <hr class="genre-hover-divider" />
    <div class="genre-hover-content">
      <span class="genre-hover-icon">
        <svg
          class="h-5 w-5 text-gray-700 dark:text-gray-300"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          stroke-width="2"
          stroke="currentColor"
          fill="none"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path stroke="none" d="M0 0h24v24H0z" />
          <circle cx="12" cy="12" r="9" />
          <line x1="12" y1="8" x2="12.01" y2="8" />
          <polyline points="11 12 12 12 12 16 13 16" />
        </svg>
      </span>
      <p class="genre-hover-desc">
        {{
          genreHovered != null
            ? genreHovered.description
            : "Nhiều thể loại từ ngôn tình đến tiên hiệp"
        }}
      </p>
    </div>
  </div>
  <!-- <div class="genre-list-header">
    <span class="genre-list-title">Thể loại</span>
    <hr class="genre-list-divider" />
  </div> -->
  <ng-container *ngFor="let group of groupGenres">
    <div class="genre-list-header">
      <span class="genre-list-title">{{ group.title }}</span>
      <hr class="genre-list-divider" />
    </div>
    <div
      class="h-full gap-2 grid grid-cols-2 @xs/main:grid-cols-3 @sm/main:grid-cols-4 @md/main:grid-cols-5 @xl/main:grid-cols-6 w-full"
    >
      <a
        class="genre-list-item"
        *ngFor="let genre of filteredGenres![group.id]"
        [ngClass]="{
          'genre-list-item-normal': !statusGenres[genre.id],
          'genre-list-item-active': statusGenres[genre.id] === 1,
          'genre-list-item-disabled': statusGenres[genre.id] === 2
        }"
        [routerLink]="routerLinkGenres ? ['tim-kiem'] : null"
        [queryParams]="routerLinkGenres ? { genres: genre.id } : null"
        (mouseover)="genreHovered = genre"
        (click)="onSelectGenre(genre)"
      >
        <!-- <i class="fas fa-dragon mr-2"></i> -->
        <div class="flex-center w-full text-sm">
          <span class="genre-title">{{ genre.title }}</span>
          <!-- <div class="text-gray-400 text-sm">6.1k</div> -->
        </div>
      </a>
    </div>
  </ng-container>
</div>
