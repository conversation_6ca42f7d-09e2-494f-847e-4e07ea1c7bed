<ng-container *ngIf="novel">

  <div
    class="detail-bg-image"
    [style.background-image]="'url(' + novel.coverImage + ')'"
  >
    <div
      class="detail-bg-image-overlay"
    ></div>
  </div>

  <div class="detail-container">
    <div class="relative w-full flex flex-col items-center mt-20 md:mt-56">
      <!-- Background -->
      <div
        class="pb-2 md:absolute md:top-0 md:-translate-y-full z-10 mb-32 pl-0 md:pl-[17.5rem] xl:pl-[21.5rem] w-full flex flex-col items-center md:items-start justify-center text-white"
      >
        <h1 class="detail-novel-title tracking-tight drop-shadow-md">
          {{ novel.title }}
        </h1>
        <a
          [routerLink]="['/truyen', novel.url + '-' + novel.id]"
          class="flex-start gap-2 text-white/90 hover:text-white hover:underline underline-offset-4 decoration-2 decoration-primary-300/60 transition-colors"
          title="Xem thông tin tác giả {{ novel.author }}"
          rel="bookmark"
        >
          <app-icon-author class="size-5"> </app-icon-author>
          <h2 class="sm:text-lg line-clamp-1">
            {{ novel.author }}
          </h2>
        </a>
      </div>

      <div
        class="relative bg-background-100 dark:bg-dark-background rounded-t-2xl px-4 xl:px-20 py-2 w-full flex flex-col items-center"
      >
        <div
          class="flex w-full z-10 items-center md:items-start flex-col md:flex-row"
        >
          <div class="w-48 md:w-56 h-48 relative flex-shrink-0">
            <div
              class="card-detail-img p-1 bg-background-100 dark:bg-dark-background absolute flex top-0 -translate-y-32 w-full h-72 md:h-80 right-0 overflow-hidden ring-1 ring-black/5 dark:ring-white/10"
            >
              <img
                loading="lazy"
                class="size-full object-cover shadow-md shadow-gray-500/30"
                [src]="novel.coverImage"
                [alt]="'Bìa truyện ' + novel.title + ' - ' + novel.author"
                [title]="novel.title"
                width="224"
                height="320"
                (error)="onImageError($event)"
                fetchpriority="high"
              />
            </div>
            <div
              (click)="onFollow()"
              [ngClass]="{
                'bg-emerald-500 hover:bg-emerald-600': this.novel.isFollow,
                'bg-secondary-100 hover:bg-secondary-200': !this.novel.isFollow
              }"
              class="text-white transition absolute shadow-sm bottom-3 md:-bottom-9 left-1/2 -translate-x-1/2 size-10 md:size-14 cursor-pointer rounded-full flex-center ring-1 ring-black/5 dark:ring-white/10 hover:scale-105"
              title="Theo dõi truyện"
              aria-label="Theo dõi truyện"
            >
              @if (novel.isFollow) {
              <svg
                class="size-5 md:size-6"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                stroke-width="4"
                stroke="currentColor"
                fill="none"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path stroke="none" d="M0 0h24v24H0z" />
                <path d="M5 12l5 5l10 -10" />
              </svg>
              } @else {
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="size-4 md:size-5"
                viewBox="0 0 18 18"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M6 2C6 1.46957 6.21071 0.960859 6.58579 0.585786C6.96086 0.210714 7.46957 0 8 0H10C10.5304 0 11.0391 0.210714 11.4142 0.585786C11.7893 0.960859 12 1.46957 12 2V6H16C16.5304 6 17.0391 6.21071 17.4142 6.58579C17.7893 6.960859 18 7.46957 18 8V10C18 10.5304 17.7893 11.0391 17.4142 11.4142C17.0391 11.7893 16.5304 12 16 12H12V16C12 16.5304 11.7893 17.0391 11.4142 17.4142C11.0391 17.7893 10.5304 18 10 18H8C7.46957 18 6.96086 17.7893 6.58579 17.4142C6.21071 17.0391 6 16.5304 6 16V12H2C1.46957 12 0.960859 11.7893 0.585786 11.4142C0.210714 11.0391 0 10.5304 0 10V8C0 7.46957 0.210714 6.96086 0.585786 6.58579C0.960859 6.21071 1.46957 6 2 6H6V2Z"
                />
              </svg>
              }
            </div>
          </div>
          <!-- Thông tin -->
          <div
            class="items-center md:items-start flex-col md:flex-row flex w-full"
          >
            <div
              class="items-center md:items-start w-full md:pl-10 flex-col flex gap-3 mt-3"
            >
              <div class="flex-start gap-2 sm:gap-4 text-sm">
                <div class="detail-status-tag">
                  <div class="dark:text-white rounded-full flex-center">
                    <svg
                      class="size-5 text-center"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                      />
                    </svg>
                  </div>
                  <span class="text-xs"> {{ novel.numChapter }} Chương </span>
                </div>
                <div class="detail-status-tag">
                  <div class="dark:text-white rounded-full flex-center">
                    <svg
                      class="size-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                  </div>
                  <span class="text-xs">
                    <!-- {{ novel.viewCount | numeral }} -->
                    N/A Lượt xem
                  </span>
                </div>
                <div class="detail-status-tag">
                  <div class="dark:text-white rounded-full flex-center">
                    <svg
                      class="size-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                      />
                    </svg>
                  </div>
                  <span class="text-xs"> {{ novel.rating }} Điểm </span>
                </div>
                <div class="detail-status-tag">
                  <div class="dark:text-white flex-center">
                    <span class="relative flex">
                      <svg
                        class="size-5"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                        <g
                          id="SVGRepo_tracerCarrier"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        ></g>
                        <g id="SVGRepo_iconCarrier">
                          <path
                            d="M3 8H21M3 12H21M3 16H21M11 20H21M3 4H21"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                          ></path>
                        </g>
                      </svg>
                    </span>
                  </div>
                  <span class="text-xs">
                    {{ novel.wordCount | numeral }} Chữ</span
                  >
                </div>
              </div>
              <span
                class="flex gap-1 items-center text-neutral-600 dark:text-dark-50"
              >
                <svg
                  class="size-5"
                  fill="currentColor"
                  version="1.1"
                  id="Layer_1"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M12,22C6.5,22,2,17.5,2,12c0-0.6,0.4-1,1-1s1,0.4,1,1c0,4.4,3.6,8,8,8s8-3.6,8-8s-3.6-8-8-8C9.3,4,6.8,5.3,5.4,7.6 C5,8,4.4,8.1,4,7.8C3.5,7.5,3.4,6.9,3.7,6.4C5.5,3.7,8.7,2,12,2c5.5,0,10,4.5,10,10S17.5,22,12,22z"
                  ></path>
                  <path
                    d="M12,13c-0.6,0-1-0.4-1-1V7c0-0.6,0.4-1,1-1s1,0.4,1,1v5C13,12.6,12.6,13,12,13z"
                  ></path>
                  <path
                    d="M15,16c-0.3,0-0.5-0.1-0.7-0.3l-3-3c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l3,3c0.4,0.4,0.4,1,0,1.4C15.5,15.9,15.3,16,15,16z "
                  ></path>

                  <path
                    d="M8,8H4C3.4,8,3,7.6,3,7V3c0-0.6,0.4-1,1-1s1,0.4,1,1v3h3c0.6,0,1,0.4,1,1S8.6,8,8,8z"
                  ></path>
                </svg>
                <time class="text-xs my-1 font-medium">
                  {{ novel.updateAt | date : "dd/MM/yyyy" }}
                </time>
              </span>
              <div class="flex gap-2 text-nowrap flex-wrap justify-start">
                <a
                  [routerLink]="['/tim-kiem']"
                  [queryParams]="{ genres: tag.id }"
                  class="detail-genre-tag transition-colors hover:bg-neutral-200 dark:hover:bg-dark-600/70"
                  [title]="'Xem truyện thể loại ' + tag.title"
                  [attr.aria-label]="'Tìm kiếm truyện thể loại ' + tag.title"
                  *ngFor="let tag of novel.genres; trackBy: trackGenreBy"
                >
                  {{ tag.title }}
                </a>
                <div
                  class="flex-center gap-1 bg-neutral-100 p-2 rounded-lg border border-neutral-200 dark:bg-dark-600 dark:border-dark-500 text-neutral-700 dark:text-dark-100 shadow-sm"
                >
                  <svg
                    fill="currentColor"
                    class="size-4"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g
                      id="SVGRepo_tracerCarrier"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></g>
                    <g id="SVGRepo_iconCarrier">
                      <path
                        d="M20,3a1,1,0,0,0,0-2H4A1,1,0,0,0,4,3H5.049c.146,1.836.743,5.75,3.194,8-2.585,2.511-3.111,7.734-3.216,10H4a1,1,0,0,0,0,2H20a1,1,0,0,0,0-2H18.973c-.105-2.264-.631-7.487-3.216-10,2.451-2.252,3.048-6.166,3.194-8Zm-6.42,7.126a1,1,0,0,0,.035,1.767c2.437,1.228,3.2,6.311,3.355,9.107H7.03c.151-2.8.918-7.879,3.355-9.107a1,1,0,0,0,.035-1.767C7.881,8.717,7.227,4.844,7.058,3h9.884C16.773,4.844,16.119,8.717,13.58,10.126ZM12,13s3,2.4,3,3.6V20H9V16.6C9,15.4,12,13,12,13Z"
                      ></path>
                    </g>
                  </svg>
                  <span class="text-xs">
                    ~{{ novel.wordCount | readTime }} đọc
                  </span>
                </div>
                <div
                  class="flex-center gap-2 bg-neutral-100 p-2 rounded-lg border border-neutral-200 dark:bg-dark-600 dark:border-dark-500 text-neutral-700 dark:text-dark-100 shadow-sm"
                >
                  <svg
                    [ngClass]="
                      novel.status === 1 ? 'text-lime-500' : 'text-cyan-500'
                    "
                    class="size-4"
                    viewBox="0 0 1024 1024"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="currentColor"
                  >
                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                    <g
                      id="SVGRepo_tracerCarrier"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    ></g>
                    <g id="SVGRepo_iconCarrier">
                      <path
                        fill="currentColor"
                        d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336L456.192 600.384z"
                      ></path>
                    </g>
                  </svg>
                  <span class="text-xs font-medium">
                    {{ novel.status === 1 ? "Hoàn thành" : "Đang tiến hành" }}
                  </span>
                </div>
              </div>

              <div class="flex-center flex-col lg:flex-row gap-4 w-full">
                <div class="flex w-full gap-2 justify-center lg:justify-start">
                  <button
                    type="button"
                    (click)="onReadButtonClick(novel)"
                    class="text-white rounded-md bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 focus:ring-4 focus:outline-none focus:ring-rose-300 font-semibold text-sm px-5 py-2.5 text-center inline-flex items-center gap-2 shadow-sm hover:shadow"
                    aria-label="Đọc ngay"
                  >
                    <p class="font-bold text-white">ĐỌC NGAY</p>
                    <svg
                      class="h-5 w-5"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      stroke-width="2"
                      stroke="currentColor"
                      fill="none"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    >
                      <path stroke="none" d="M0 0h24v24H0z" />
                      <line x1="17" y1="7" x2="7" y2="17" />
                      <polyline points="8 7 17 7 17 16" />
                    </svg>
                  </button>
                  <button
                    type="button"
                    class="text-primary-600 dark:text-primary-300 border-2 border-primary-200 dark:border-primary-400 rounded-md focus:ring-4 focus:outline-none focus:ring-primary-300/40 font-semibold text-sm px-5 py-2 text-center inline-flex items-center gap-2 hover:bg-primary-50/60 dark:hover:bg-primary-400/10"
                    aria-label="Nghe truyện"
                  >
                    <p class="font-bold text-primary-100">NGHE TRUYỆN</p>
                  </button>
                </div>
                <div class="flex gap-3 w-full justify-center lg:justify-start">
                  <button
                    class="flex flex-col items-center space-x-1 space-y-1"
                    aria-label="Báo lỗi"
                    title="Báo lỗi"
                  >
                    <div
                      (click)="onReport()"
                      class="size-10 bg-neutral-100 hover:bg-neutral-200 dark:bg-dark-600 dark:hover:bg-dark-700 dark:text-white rounded-full flex-center ring-1 ring-neutral-200 dark:ring-dark-500 transition"
                    >
                      <svg
                        class="h-5 w-5 text-yellow-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                        />
                      </svg>
                    </div>
                    <!-- <span class="text-xs text-yellow-500"> Báo lỗi </span> -->
                  </button>
                  <button
                    (click)="onDownload()"
                    class="flex flex-col items-center space-x-1 space-y-1"
                    aria-label="Tải truyện"
                    title="Tải truyện"
                  >
                    <div
                      class="size-10 bg-neutral-100 hover:bg-neutral-200 dark:bg-dark-600 dark:hover:bg-dark-700 dark:text-white rounded-full flex-center ring-1 ring-neutral-200 dark:ring-dark-500 transition"
                    >
                      <svg
                        class="h-5 w-5 text-current"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        stroke-width="2"
                        stroke="currentColor"
                        fill="none"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path stroke="none" d="M0 0h24v24H0z" />
                        <path d="M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2" />
                        <polyline points="7 11 12 16 17 11" />
                        <line x1="12" y1="4" x2="12" y2="16" />
                      </svg>
                    </div>
                    <!-- <span class="text-xs"> Ebook </span> -->
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div
      class="bg-white absolute bottom-0 w-full h-1/3 rounded-lg"
    ></div> -->
    </div>
    <div class="py-2 px-4 flex flex-col gap-4">
      <div
        class="mt-5 md:mt-10 xl:px-16 col-span-1 flex flex-col justify-center"
      >
        <h2 class="text-xl font-semibold tracking-wide flex-start">
          <svg
            class="size-5 mr-2 "
            viewBox="0 0 64 64"
            fill="#000000"
          >
            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
            <g
              id="SVGRepo_tracerCarrier"
              stroke-linecap="round"
              stroke-linejoin="round"
            ></g>
            <g id="SVGRepo_iconCarrier">
              <path
                fill="#231F20"
                d="M60,52V4c0-2.211-1.789-4-4-4H14v51v3h42v8H10c-2.209,0-4-1.791-4-4s1.791-4,4-4h2v-3V0H8 C5.789,0,4,1.789,4,4v54c0,3.313,2.687,6,6,6h49c0.553,0,1-0.447,1-1s-0.447-1-1-1h-1v-8C59.104,54,60,53.104,60,52z M23,14h12 c0.553,0,1,0.447,1,1s-0.447,1-1,1H23c-0.553,0-1-0.447-1-1S22.447,14,23,14z M42,28H23c-0.553,0-1-0.447-1-1s0.447-1,1-1h19 c0.553,0,1,0.447,1,1S42.553,28,42,28z M49,22H23c-0.553,0-1-0.447-1-1s0.447-1,1-1h26c0.553,0,1,0.447,1,1S49.553,22,49,22z"
              ></path>
            </g>
          </svg>
          GIỚI THIỆU TRUYỆN
        </h2>
        <span
          class="md:px-2 text-neutral-800 dark:text-dark-100 text-[15px] leading-7 text-justify"
          [ngClass]="{ 'line-clamp-4': !descriptionExpanded }"
          [innerHTML]="
            novel.description
              | defaultDescription : novel.id : novel.title : novel.url : true
          "
        ></span>
        <button
          (click)="toggleDescription()"
          class="w-fit px-2 py-1 rounded-md border text-sm text-neutral-600 dark:text-white font-medium cursor-pointer border-neutral-200 dark:border-dark-500 hover:bg-neutral-100 dark:hover:bg-dark-600/60 transition-colors"
          aria-label="Xem thêm"
        >
          {{ descriptionExpanded ? "Ẩn" : "Xem thêm" }}
        </button>
      </div>
      <div class="mt-2 grid grid-cols-1 lg:grid-cols-12 gap-4 md:gap-6">
        <div class="lg:col-span-8">
          <div class="h-full">
            <app-chapter-list [novel]="novel"></app-chapter-list>
          </div>
        </div>

        <div class="mt-2 lg:mt-0 lg:col-span-4">
          <span class="inline-flex items-center gap-2">
            <svg
              class="size-6"
              focusable="false"
              viewBox="0 0 24 24"
              fill="currentColor"
              data-testid="StarsIcon"
            >
              <path
                d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm4.24 16L12 15.45 7.77 18l1.12-4.81-3.73-3.23 4.92-.42L12 5l1.92 4.53 4.92.42-3.73 3.23L16.23 18z"
              ></path>
            </svg>
            <h2 class="text-xl font-semibold tracking-wide">ĐỒNG TÁC GIẢ</h2>
          </span>
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-2 mt-3"
          >
            <!-- Loading state for co-author novels -->
            <div *ngIf="isLoadingCoAuthor" class="flex justify-center py-8">
              <div
                class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-200"
              ></div>
            </div>

            <ng-container
              *ngIf="
                !isLoadingCoAuthor &&
                  sameAuthorNovels &&
                  sameAuthorNovels.length > 0;
                else noCoAuthor
              "
            >
              <ng-container
                *ngFor="let novel of sameAuthorNovels; trackBy: trackNovelBy"
              >
                <app-card-v2
                  [size]="'cardv2-small'"
                  [novel]="novel"
                ></app-card-v2>
              </ng-container>
            </ng-container>
            <ng-template #noCoAuthor>
              <div
                *ngIf="!isLoadingCoAuthor"
                class="flex-center flex-col py-6 rounded-lg bg-neutral-50 dark:bg-dark-700 border border-dashed border-neutral-300 dark:border-dark-400 shadow-sm"
              >
                <svg
                  class="size-8 mb-2 text-gray-300 dark:text-dark-200"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <circle
                    cx="12"
                    cy="12"
                    r="10"
                    stroke-width="2"
                    stroke="currentColor"
                    fill="none"
                  />
                </svg>
                <span
                  class="text-base font-medium text-gray-500 dark:text-dark-100"
                  >Không có truyện đồng tác giả</span
                >
                <span class="p-4 text-xs text-gray-400 dark:text-dark-200 mt-1"
                  >Tác giả {{ novel.author }} chỉ có truyện này duy nhất.</span
                >
              </div>
            </ng-template>
          </div>
        </div>
      </div>
    </div>

    <div class="px-4 flex flex-col mt-6">
      <span class="inline-flex items-center gap-2">
        <svg
          class="size-6"
          focusable="false"
          viewBox="0 0 24 24"
          data-testid="StarsIcon"
          fill="currentColor"
        >
          <path
            d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zm4.24 16L12 15.45 7.77 18l1.12-4.81-3.73-3.23 4.92-.42L12 5l1.92 4.53 4.92.42-3.73 3.23L16.23 18z"
          ></path>
        </svg>
        <h2 class="text-xl font-semibold tracking-wide">TRUYỆN LIÊN QUAN</h2>
      </span>
      <div
        class="mt-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-3 sm:gap-4"
      >
        <!-- Loading state for similar novels -->
        <div
          *ngIf="isLoadingSimilarNovel"
          class="col-span-full flex justify-center py-8"
        >
          <div
            class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-200"
          ></div>
        </div>

        <ng-container *ngIf="!isLoadingSimilarNovel">
          <ng-container
            *ngFor="
              let novel of similarNovels | slice : 0 : 6;
              trackBy: trackNovelBy
            "
          >
            <app-card-v1 [novel]="novel"></app-card-v1>
          </ng-container>
        </ng-container>
      </div>
    </div>

    <!-- FAQ Section - Temporarily commented out -->
    <!-- <div class="px-4 mt-8">
      <app-faq
        [faqs]="getNovelFAQs()"
        [title]="'Câu Hỏi Về Truyện ' + (novel.title || '')"
        [showStructuredData]="true"
      ></app-faq>
    </div> -->

    <div *ngIf="!isServer()" class="flex flex-col mt-4 w-full">
      <app-comment [novelId]="novel.id"> </app-comment>
    </div>
  </div>
</ng-container>
