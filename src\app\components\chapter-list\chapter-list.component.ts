import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Inject, Input, PLATFORM_ID, SimpleChanges, ViewChild } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { SelectComponent } from '@components/select/select.component';
import { SpinnerComponent } from '@components/spinner/spinner.component';
import { Chapter } from '@schemas/Chapter';
import { ChapterList } from '@schemas/ChapterList';
import { IOption } from '@schemas/IOption';
import { Novel } from '@schemas/Novel';
import { IServiceResponse } from '@schemas/ResponseType';
import { NovelService } from '@services/novel.service';

@Component({
  selector: 'app-chapter-list',
  standalone: true,
  templateUrl: './chapter-list.component.html',
  styleUrl: './chapter-list.component.scss',
  imports: [
    CommonModule,
    RouterLink,
    SelectComponent,
    SpinnerComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,

})
export class ChapterListComponent {

  @Input()
  novel?: Novel;

  @ViewChild('searchChapterInput')
  searchChapterInput!: ElementRef<HTMLInputElement>;
  @ViewChild('overlay')
  overlayEl!: ElementRef<HTMLElement>;
  @Input() allchapters: Chapter[] = [];
  history: number[] = [];

  options: IOption[] = [{
    label: '0 - 100',
    value: 1
  }];
  @Input()
  isChapterLoading = false;

  totalChapterPage = 1
  sizePage = 200
  currentPage = 1

  isIncrease = true;
  constructor(
    private router: Router,
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Object,
    private novelService: NovelService
  ) { }
  ngOnInit() {
    this.FetchChapters();

  }

  FetchChapters() {
    // if(isPlatformServer(this.platformId)) return
    
    this.isChapterLoading = true;
    this.novelService.getChapters(this.novel?.id!, this.currentPage, this.sizePage).subscribe((res: IServiceResponse<ChapterList>) => {
      this.isChapterLoading = false;
      if (!res.data) return      
      this.allchapters = [...res.data.chapters];
      if (!this.isIncrease) {
        this.allchapters.reverse();
      }
      this.totalChapterPage = res.data.totalpage;
      this.sizePage = res.data.step;
      const sign = this.isIncrease ? 1 : -1;
      const length = this.isIncrease ? 0 : this.totalChapterPage - 1
      this.options = Array.from(
        { length: this.totalChapterPage },
        (_, i) => {

          return {
            label: `${(length + i * sign) * this.sizePage} - ${(length + i * sign + 1) * this.sizePage}`,
            value: length + i * sign + 1,
          };
        }
      )
      this.cd.detectChanges();

    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['novel'] && !changes['novel'].firstChange) {
      this.FetchChapters();
    }

  }
  ngAfterViewInit() {
    // if (isPlatformBrowser(this.platformId)) {
    //   if (this.allchapters) {
    //     this.cd.detectChanges();
    //   }
    // }
  }

  onPageChange(value: number) {
    this.currentPage = value;
    this.FetchChapters();
  }
  onOrderChange($event: Event) {

    this.isIncrease = ($event.target as HTMLInputElement).checked

    if (this.isIncrease) {
      this.currentPage = 1
    }
    else {
      this.currentPage = this.totalChapterPage
    }
    this.FetchChapters();

  }

}
