import { Component, ElementRef, EventEmitter, HostListener, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';

@Component({
    standalone: true,
    template: ``,
    imports: [FormsModule]
})
export class PopupComponent {
    @Input() title = ''
    @Input() visible = false
    @Input() hover = false

    @Output() visibleChange = new EventEmitter<boolean>()

    constructor(public ref: ElementRef<HTMLElement>) {

    }

    close() {
        if (this.visible) {
            this.visible = false
            this.visibleChange.emit(false)
        }
    }
    open() {
        this.visible = true
        this.visibleChange.emit(true)
    }
    toggle() {
        if (this.visible) {
            this.close()
        } else {
            this.open()
        }
    }
    ngOnInit() {

    }

    ngAfterViewInit() {
    }
}
