// @import '@ctrl/ngx-emoji-mart/picker';
@use './styles/common.scss' as *;
@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

:root,
html,
body {
  font-family: "Be Vietnam Pro", sans-serif;
  overflow-x: hidden;
  scroll-behavior: smooth !important;
  font-weight: 300;
  --color-primary-100: #e35f66;
  --color-primary-200: #c03f4b;
  --color-primary-300: #9d1d31;
  --color-background-100: #f5f5f5;
  --color-background-200: #f5f5f5;
  --color-background-300: #f5f5f5;
}


/* Track */
::-webkit-scrollbar-track {
  // background: #f1f1f1;
  background-color: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
  @apply bg-neutral-200 dark:bg-neutral-600 rounded-lg cursor-pointer;
}



.genre-tag {
  @apply border-primary-100 border text-primary-300 text-xs font-normal bg-neutral-300/20 dark:bg-dark-650 dark:text-dark-50 rounded-md shadow-sm px-2 py-0.5 uppercase;
}

.common-container {
  @apply md:container mx-auto p-2 mt-20;
}

.brand-name {
  font-weight: 700;
  background: linear-gradient(135deg, #ea580c, #c65f5f);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}


/* add the code bellow */
@layer utilities {

  .scrollbar-style-lg::-webkit-scrollbar {
      width: 8px;
      height: 8px;
  }
  .scrollbar-style-md::-webkit-scrollbar {
      width: 6px;
      height: 6px;
  }
  .scrollbar-style-sm::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }
  /* === Size === */


  /* === FLEX HELPERS === */

  .inline-flex-center {
    @apply inline-flex items-center justify-center;
  }

  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-between {
    @apply flex items-center justify-between;
  }

  .flex-start {
    @apply flex items-center justify-start;
  }

  .flex-end {
    @apply flex items-center justify-end;
  }

  .flex-col-center {
    @apply flex flex-col items-center justify-center;
  }

  .flex-col-between {
    @apply flex flex-col justify-between;
  }

  /* === GRID HELPERS === */
  .grid-auto-fit {
    @apply grid grid-cols-[repeat(auto-fit, minmax(250px, 1fr))];
  }

  .grid-auto-fill {
    @apply grid grid-cols-[repeat(auto-fill, minmax(200px, 1fr))];
  }

  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .grid-cards {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* === Border HELPERS === */
  .border-common {
    @apply border border-neutral-200 dark:border-neutral-700 rounded-lg;
  }
}