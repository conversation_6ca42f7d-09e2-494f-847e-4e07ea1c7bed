import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, ChangeDetectionStrategy, ViewEncapsulation } from '@angular/core';
import { RouterLink } from '@angular/router';
import { SeoService } from '@services/seo.service';
import { NovelService } from '@services/novel.service';
import { FaqComponent, FAQItem } from '@components/faq/faq.component';
import { NovelCardV1Component } from '@components/novel-card/card-v1/card-v1.component';
import { Novel } from '@schemas/Novel';
import { Genre } from '@schemas/Genre';
import globalConfig from 'globalConfig';

@Component({
  selector: 'app-category-content',
  templateUrl: './category-content.component.html',
  styleUrls: ['./category-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule, 
    RouterLink, 
    FaqComponent, 
    NovelCardV1Component
  ]
})
export class CategoryContentComponent implements OnInit {
  @Input() genre!: Genre;
  @Input() novels: Novel[] = [];
  @Input() totalNovels: number = 0;

  relatedGenres: Genre[] = [];
  topNovels: Novel[] = [];
  newNovels: Novel[] = [];
  completedNovels: Novel[] = [];

  constructor(
    private seoService: SeoService,
    private novelService: NovelService
  ) {}

  ngOnInit() {
    if (this.genre) {
      this.setupCategoryContent();
      this.loadRelatedData();
    }
  }

  private setupCategoryContent() {
    // Setup category-specific content based on genre
    this.setupGenreSpecificContent();
  }

  private setupGenreSpecificContent() {
    // This will be expanded based on specific genres
    switch (this.genre.slug) {
      case 'ngon-tinh':
        this.setupNgonTinhContent();
        break;
      case 'tien-hiep':
        this.setupTienHiepContent();
        break;
      case 'kiem-hiep':
        this.setupKiemHiepContent();
        break;
      case 'do-thi':
        this.setupDoThiContent();
        break;
      case 'huyen-huyen':
        this.setupHuyenHuyenContent();
        break;
      case 'xuyen-khong':
        this.setupXuyenKhongContent();
        break;
      default:
        this.setupDefaultContent();
    }
  }

  private setupNgonTinhContent() {
    // Ngôn tình specific content
  }

  private setupTienHiepContent() {
    // Tiên hiệp specific content
  }

  private setupKiemHiepContent() {
    // Kiếm hiệp specific content
  }

  private setupDoThiContent() {
    // Đô thị specific content
  }

  private setupHuyenHuyenContent() {
    // Huyền huyễn specific content
  }

  private setupXuyenKhongContent() {
    // Xuyên không specific content
  }

  private setupDefaultContent() {
    // Default content for other genres
  }

  private loadRelatedData() {
    // Load related genres, top novels, etc.
    this.loadTopNovels();
    this.loadNewNovels();
    this.loadCompletedNovels();
  }

  private loadTopNovels() {
    // Load top novels for this genre
  }

  private loadNewNovels() {
    // Load newest novels for this genre
  }

  private loadCompletedNovels() {
    // Load completed novels for this genre
  }

  getFAQsForGenre(): FAQItem[] {
    const baseGenreName = this.genre.title.toLowerCase();
    
    return [
      {
        question: `Truyện ${this.genre.title} hay nhất hiện nay là gì?`,
        answer: `Hiện tại có rất nhiều bộ truyện ${this.genre.title} hay được độc giả yêu thích tại SayTruyenHot. Bạn có thể khám phá danh sách <strong>Top truyện ${this.genre.title}</strong> được cập nhật hàng ngày để tìm những bộ truyện chất lượng nhất.`,
        category: 'recommendations'
      },
      {
        question: `Làm sao để tìm truyện ${this.genre.title} phù hợp với sở thích?`,
        answer: `SayTruyenHot cung cấp <strong>hệ thống lọc nâng cao</strong> giúp bạn tìm truyện ${this.genre.title} theo nhiều tiêu chí: tác giả, trạng thái hoàn thành, số chương, đánh giá. Bạn cũng có thể xem <strong>truyện đề xuất</strong> dựa trên lịch sử đọc.`,
        category: 'search'
      },
      {
        question: `Truyện ${this.genre.title} nào đang hot nhất tuần này?`,
        answer: `Bảng xếp hạng <strong>Top ${this.genre.title} tuần</strong> được cập nhật liên tục dựa trên lượt đọc, bình luận và đánh giá của độc giả. Hãy theo dõi mục "Trending" để không bỏ lỡ những bộ truyện ${baseGenreName} hot nhất.`,
        category: 'trending'
      },
      {
        question: `Có bao nhiêu bộ truyện ${this.genre.title} tại SayTruyenHot?`,
        answer: `SayTruyenHot hiện có <strong>hàng nghìn bộ truyện ${this.genre.title}</strong> từ nhiều tác giả khác nhau, bao gồm cả truyện đã hoàn thành và đang cập nhật. Kho truyện được bổ sung thêm nội dung mới hàng ngày.`,
        category: 'collection'
      }
    ];
  }

  getGenreDescription(): string {
    if (!this.genre?.slug) {
      return 'Thể loại truyện với những câu chuyện hấp dẫn và đa dạng nội dung.';
    }

    const descriptions: { [key: string]: string } = {
      'ngon-tinh': 'Thể loại truyện tình cảm lãng mạn, kể về những câu chuyện tình yêu ngọt ngào, đôi khi có drama và xung đột nhưng luôn có kết thúc có hậu.',
      'tien-hiep': 'Thể loại truyện về tu tiên, tu ma, với thế giới huyền bí đầy phép thuật, võ công cao cường và hành trình tu luyện vươn lên đỉnh cao.',
      'kiem-hiep': 'Thể loại truyện võ hiệp cổ điển với bối cảnh giang hồ, các cao thủ võ lâm, kiếm pháp huyền diệu và nghĩa khí anh hùng.',
      'do-thi': 'Thể loại truyện với bối cảnh hiện đại, kể về cuộc sống đô thị, tình yêu, sự nghiệp và những mối quan hệ phức tạp trong xã hội hiện tại.',
      'huyen-huyen': 'Thể loại truyện kỳ ảo với thế giới ma pháp, sinh vật huyền bí, phép thuật và những cuộc phiêu lưu trong các thế giới khác.',
      'xuyen-khong': 'Thể loại truyện về du hành thời gian, xuyên việt đến các thời đại khác nhau, thường kết hợp với yếu tố tình cảm và phiêu lưu.'
    };

    return descriptions[this.genre.slug] || `Thể loại ${this.genre.title} với những câu chuyện hấp dẫn và đa dạng nội dung.`;
  }

  getPopularTags(): string[] {
    if (!this.genre?.slug) {
      return ['truyện hay', 'nội dung hấp dẫn', 'cập nhật thường xuyên'];
    }

    const tagsByGenre: { [key: string]: string[] } = {
      'ngon-tinh': ['tình yêu', 'lãng mạn', 'ngọt ngào', 'drama', 'happy ending', 'CEO', 'học đường'],
      'tien-hiep': ['tu tiên', 'tu ma', 'phép thuật', 'võ công', 'đan dược', 'linh thú', 'thăng thiên'],
      'kiem-hiep': ['giang hồ', 'võ lâm', 'kiếm pháp', 'cao thủ', 'bang phái', 'nghĩa khí', 'báo thù'],
      'do-thi': ['hiện đại', 'tình yêu', 'sự nghiệp', 'gia đình', 'bạn bè', 'công sở', 'cuộc sống'],
      'huyen-huyen': ['ma pháp', 'kỳ ảo', 'sinh vật', 'phiêu lưu', 'thế giới khác', 'sức mạnh', 'huyền bí'],
      'xuyen-khong': ['xuyên việt', 'thời gian', 'cổ đại', 'hiện đại', 'tương lai', 'trọng sinh', 'hệ thống']
    };

    return tagsByGenre[this.genre.slug] || ['truyện hay', 'nội dung hấp dẫn', 'cập nhật thường xuyên'];
  }

  trackByNovel(_index: number, novel: Novel): number {
    return novel.id;
  }
}
