<div class="min-h-screen bg-background-100 dark:bg-dark-background flex items-center justify-center px-4 py-8">
  <div class="max-w-lg mx-auto text-center relative">
    <!-- Animated 404 Illustration -->
    <div class="relative mb-8">
      <div class="relative">
        <!-- Main 404 Text with Book Theme -->
        <div class="relative z-10">
          <h1 class="text-8xl md:text-9xl font-black text-primary-100 dark:text-primary-200 opacity-20 leading-none">
            404
          </h1>
          <div class="absolute inset-0 flex items-center justify-center">
            <!-- Book Stack Illustration -->
            <div class="relative">
              <div class="w-16 h-20 bg-gradient-to-br from-primary-100 to-primary-200 rounded-sm transform rotate-3 shadow-lg"></div>
              <div class="w-16 h-20 bg-gradient-to-br from-secondary-100 to-secondary-200 rounded-sm transform -rotate-2 shadow-lg absolute top-1 left-2"></div>
              <div class="w-16 h-20 bg-gradient-to-br from-amber-400 to-amber-500 rounded-sm transform rotate-1 shadow-lg absolute top-2 left-4"></div>
              
              <!-- Flying Page Animation -->
              <div class="absolute -top-4 -right-8 w-8 h-10 bg-white dark:bg-dark-600 rounded-sm transform rotate-12 shadow-md animate-bounce">
                <div class="w-6 h-0.5 bg-gray-300 dark:bg-dark-400 mt-2 mx-1"></div>
                <div class="w-4 h-0.5 bg-gray-300 dark:bg-dark-400 mt-1 mx-1"></div>
                <div class="w-6 h-0.5 bg-gray-300 dark:bg-dark-400 mt-1 mx-1"></div>
                <div class="w-3 h-0.5 bg-gray-300 dark:bg-dark-400 mt-1 mx-1"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="space-y-6">
      <div class="space-y-3">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
          Trang Không Tìm Thấy
        </h2>
        <div class="flex items-center justify-center gap-2 text-primary-100 dark:text-primary-200">
          <svg class="size-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
          <span class="text-sm font-medium">Có vẻ như bạn đã lạc đường rồi!</span>
        </div>
      </div>

      <p class="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
        Trang bạn đang tìm kiếm có thể đã bị xóa, di chuyển hoặc không tồn tại. 
        <br class="hidden sm:block">
        Đừng lo lắng, hãy khám phá thế giới truyện chữ tuyệt vời của chúng tôi!
      </p>

      <!-- Quick Actions -->
      <div class="space-y-4">
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            routerLink="/"
            class="group inline-flex items-center justify-center gap-2 px-6 py-3 bg-primary-100 hover:bg-primary-200 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200"
          >
            <svg class="size-5 group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Về Trang Chủ
          </a>
          
          <a
            routerLink="/tim-kiem"
            class="group inline-flex items-center justify-center gap-2 px-6 py-3 bg-white dark:bg-dark-600 hover:bg-gray-50 dark:hover:bg-dark-500 text-gray-900 dark:text-white font-semibold rounded-xl border-2 border-gray-200 dark:border-dark-400 hover:border-primary-100 dark:hover:border-primary-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 transition-all duration-200"
          >
            <svg class="size-5 group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            Tìm Kiếm Truyện
          </a>
        </div>

        <!-- Popular Genres Quick Links -->
        <div class="pt-6 border-t border-gray-200 dark:border-dark-500">
          <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Hoặc khám phá các thể loại phổ biến:
          </p>
          <div class="flex flex-wrap justify-center gap-2">
            <a 
              routerLink="/tim-kiem" 
              [queryParams]="{ genres: '50' }"
              class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 text-blue-700 dark:text-blue-300 text-sm font-medium rounded-full hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-800 dark:hover:to-blue-700 transition-all duration-200"
            >
              Tiên Hiệp
            </a>
            <a 
              routerLink="/tim-kiem" 
              [queryParams]="{ genres: '35' }"
              class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-pink-50 to-pink-100 dark:from-pink-900 dark:to-pink-800 text-pink-700 dark:text-pink-300 text-sm font-medium rounded-full hover:from-pink-100 hover:to-pink-200 dark:hover:from-pink-800 dark:hover:to-pink-700 transition-all duration-200"
            >
              Ngôn Tình
            </a>
            <a 
              routerLink="/tim-kiem" 
              [queryParams]="{ genres: '15' }"
              class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800 text-purple-700 dark:text-purple-300 text-sm font-medium rounded-full hover:from-purple-100 hover:to-purple-200 dark:hover:from-purple-800 dark:hover:to-purple-700 transition-all duration-200"
            >
              Huyền Huyễn
            </a>
            <a 
              routerLink="/tim-kiem" 
              [queryParams]="{ genres: '27' }"
              class="inline-flex items-center px-3 py-1.5 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900 dark:to-green-800 text-green-700 dark:text-green-300 text-sm font-medium rounded-full hover:from-green-100 hover:to-green-200 dark:hover:from-green-800 dark:hover:to-green-700 transition-all duration-200"
            >
              Kiếm Hiệp
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Decorative Elements -->
    <div class="absolute top-10 left-10 opacity-30">
      <div class="w-16 h-16 bg-primary-100 dark:bg-primary-200 rounded-full animate-pulse"></div>
    </div>
    <div class="absolute bottom-20 right-10 opacity-20">
      <div class="w-8 h-8 bg-secondary-100 dark:bg-secondary-200 rounded-full animate-bounce" style="animation-delay: 0.5s"></div>
    </div>
    <div class="absolute top-1/3 right-20 opacity-25">
      <div class="w-12 h-12 bg-amber-400 dark:bg-amber-500 rounded-full animate-pulse" style="animation-delay: 1s"></div>
    </div>
  </div>
</div>
