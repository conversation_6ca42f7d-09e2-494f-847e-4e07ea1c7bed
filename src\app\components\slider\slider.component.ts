import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-slider',
  templateUrl: './slider.component.html',
  styleUrls: ['./slider.component.scss'],
  imports: [CommonModule],
})
export class SliderComponent {
  @Input()
  value: number = 50; // Giá trị mặc định của slider
  @Input()
  min = 0; // Giá trị nhỏ nhat
  @Input()
  max = 100; // Giá trị lớn nhất
  @Input()
  step = 1; // Buổi nháp giá trị
  @Output()
  valueChange = new EventEmitter<number>();
  // Hàm xử lý thay đổi giá trị slider
  onSliderChange(event:any) {
    this.value = event.target.value;
    this.valueChange.emit(this.value);
  }
}
