<div *ngIf="isVisible" [@openDialog]>
  <div
    class="fixed z-[100] size-full sm:w-fit sm:h-fit top-0 xs:top-1/2 right-1/2 translate-x-1/2 xs:-translate-y-1/2 overflow-auto scrollbar-style-lg"
  >
    <span
      (click)="close()"
      class="absolute right-2 top-2 hover:cursor-pointer rounded-full p-1.5 z-50"
    >
      <svg
        class="size-5"
        width="64px"
        height="64px"
        viewBox="0 0 16 16"
        xmlns="http://www.w3.org/2000/svg"
        fill="currentColor"
      >
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g
          id="SVGRepo_tracerCarrier"
          stroke-linecap="round"
          stroke-linejoin="round"
        ></g>
        <g id="SVGRepo_iconCarrier">
          <path
            fill="currentColor"
            fill-rule="evenodd"
            d="M11.2929,3.29289 C11.6834,2.90237 12.3166,2.90237 12.7071,3.29289 C13.0976,3.68342 13.0976,4.31658 12.7071,4.70711 L9.41421,8 L12.7071,11.2929 C13.0976,11.6834 13.0976,12.3166 12.7071,12.7071 C12.3166,13.0976 11.6834,13.0976 11.2929,12.7071 L8,9.41421 L4.70711,12.7071 C4.31658,13.0976 3.68342,13.0976 3.29289,12.7071 C2.90237,12.3166 2.90237,11.6834 3.29289,11.2929 L6.58579,8 L3.29289,4.70711 C2.90237,4.31658 2.90237,3.68342 3.29289,3.29289 C3.68342,2.90237 4.31658,2.90237 4.70711,3.29289 L8,6.58579 L11.2929,3.29289 Z"
          ></path>
        </g>
      </svg>
    </span>
    <ng-content> </ng-content>
    <!-- <div class="mx-auto w-full xs:w-fit flex relative">
    </div> -->
  </div>
  <div
    (click)="close()"
    class="fixed z-[99] right-0 bottom-0 left-0 top-0 bg-black/20 "
  ></div>
</div>
