// Animation for search box
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 0rem;
  border-top: #111827;
}


// Main pagination navigation
.pagination-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

// Previous/Next buttons
.pagination-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  text-decoration: none;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: var(--color-primary-100);
    color: var(--color-primary-100);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(227, 95, 102, 0.15);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary-100);
  }

  .pagination-icon {
    width: 1rem;
    height: 1rem;
  }

  .pagination-btn-text {
    display: none;

    @media (min-width: 640px) {
      display: inline;
    }
  }

  .dark & {
    color: #d1d5db;
    background-color: #262626;
    border-color: #525252;

    &:hover {
      background-color: #404040;
    }
  }
}

// Page numbers container
.pagination-pages {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin: 0 0.5rem;
}

// Individual page number
.pagination-page {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  text-decoration: none;
  transition: all 0.2s ease-in-out;

  &:hover {
    background-color: #fef2f2;
    border-color: #fecaca;
    color: var(--color-primary-100);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(227, 95, 102, 0.1);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary-100);
  }

  &.active {
    background-color: var(--color-primary-100);
    color: white;
    border-color: var(--color-primary-100);

    &:hover {
      background-color: var(--color-primary-200);
      transform: translateY(-1px);
    }
  }


}

.dark {
  .pagination-page {
    color: #d1d5db;
    background-color: #262626;
    border-color: #404040;

    &:hover {
      background-color: rgba(227, 95, 102, 0.1);
      border-color: rgba(227, 95, 102, 0.3);
    }
    &.active {
      background-color: var(--color-primary-100);
      color: white;
      border-color: var(--color-primary-100);

      &:hover {
        background-color: var(--color-primary-200);
        transform: translateY(-1px);
      }
    }
  }
}

// Ellipsis button
.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  color: #6b7280;
  background: transparent;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  .ellipsis-dots {
    font-size: 1.125rem;
    line-height: 1;
  }

  &:hover {
    background-color: #f3f4f6;
    color: var(--color-primary-100);
    transform: scale(1.1);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--color-primary-100);
  }

  .dark & {
    color: #9ca3af;

    &:hover {
      background-color: #404040;
    }
  }
}

// Search section
.pagination-search {
  width: 100%;
  max-width: 24rem;
  animation: slideIn 0.3s ease-out;

  .pagination-search-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .pagination-search-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
  }

  .pagination-search-group {
    display: flex;
    gap: 0.5rem;
  }

  .pagination-search-info {
    text-align: center;

    .text-xs {
      font-size: 0.75rem;
    }

    .text-neutral-500 {
      color: #6b7280;
    }

    .dark & .text-neutral-500 {
      color: #9ca3af;
    }
  }

  .pagination-search-input {
    flex: 1;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    color: #111827;
    background-color: #f9fafb;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: all 0.2s ease-in-out;

    &::placeholder {
      color: #9ca3af;
    }

    &:focus {
      outline: none;
      background-color: white;
      border-color: var(--color-primary-100);
      box-shadow: 0 0 0 3px rgba(227, 95, 102, 0.1);
    }
  }

  .pagination-search-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    color: white;
    background-color: var(--color-primary-100);
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover:not(:disabled) {
      background-color: var(--color-primary-200);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(227, 95, 102, 0.25);
    }

    &:disabled {
      background-color: #d1d5db;
      cursor: not-allowed;
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px var(--color-primary-100);
    }

    .pagination-search-icon {
      width: 1rem;
      height: 1rem;
    }
  }

  .dark & {
    .pagination-search-content {
      background-color: #262626;
      border-color: #404040;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
    }

    .pagination-search-label {
      color: #d1d5db;
    }

    .pagination-search-input {
      color: white;
      background-color: #404040;
      border-color: #525252;

      &::placeholder {
        color: #6b7280;
      }

      &:focus {
        background-color: #525252;
      }
    }
  }
}

// Responsive design
@media (max-width: 640px) {
  .pagination-container {
    gap: 0.75rem;
    padding: 1rem 0.5rem;
  }

  .pagination-nav {
    gap: 0.25rem;
  }

  .pagination-btn {
    padding: 0.5rem;
  }

  .pagination-pages {
    margin: 0 0.25rem;
  }

  .pagination-page {
    min-width: 2.25rem;
    height: 2.25rem;
    font-size: 0.75rem;
  }

  .pagination-ellipsis {
    min-width: 2.25rem;
    height: 2.25rem;
  }

  // Hide page info on mobile when search is active
  .pagination-info {
    display: none;
  }
}