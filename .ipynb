{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from paddleocr import PaddleOCR\n", "\n", "ocr = PaddleOCR(use_angle_cls=True, lang=\"vi\")  # hoặc lang='vi' nếu có tiếng Việt\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Detected: input_path\n", "Detected: page_index\n", "Detected: doc_preprocessor_res\n", "Detected: dt_polys\n", "Detected: model_settings\n", "Detected: text_det_params\n", "Detected: text_type\n", "Detected: text_rec_score_thresh\n", "Detected: rec_texts\n", "Detected: rec_scores\n", "Detected: rec_polys\n", "Detected: vis_fonts\n", "Detected: textline_orientation_angles\n", "Detected: rec_boxes\n"]}], "source": ["results = ocr.predict(\"ngay-dem-an-cuu-vi.jpg\")\n", "\n", "for line in results[0]:\n", "    text = line[1][0]\n", "    print(f\"Detected: {line}\")\n", "    if \"truyenfull.vision\" in text.lower():\n", "        print(\"✅ Found 'truyenfull.vision'\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}