from paddleocr import PaddleOCR

ocr = PaddleOCR(use_angle_cls=True, lang="vi")  # hoặc lang='vi' nếu có tiếng Việt


results = ocr.predict("ngay-dem-an-cuu-vi.jpg")

for line in results[0]:
    text = line[1][0]
    print(f"Detected: {line}")
    if "truyenfull.vision" in text.lower():
        print("✅ Found 'truyenfull.vision'")

!pip install pytrends

import httpx
import pandas as pd

def fetch_trend_json():
    params = {
        # Bạn phải điều chỉnh URL và params dựa vào link thật copy từ DevTools
        'hl': 'vi-VN',
        'tz': -420,  # tương ứng GMT+7
        'req': '{"comparisonItem":[{"keyword":"truyện chữ","geo":"VN","time":"today 12-m"}],"category":0,"property":""}',
        'tz': '420'
    }
    url = "https://trends.google.com/trends/api/widgetdata/multiline"
    resp = httpx.get(url, params=params)
    text = resp.text.lstrip(")]}',\n")  # loại bỏ prefix bảo mật nếu có
    return resp, text

response, json_str = fetch_trend_json()

if response.status_code == 200:
    data = response.json()
    # Trích xuất tập dữ liệu túm gọn về thời gian và giá trị interest
    timeline = data['default']['timelineData']
    df = pd.DataFrame([
        {
            'time': pd.to_datetime(item['time'], unit='s'),
            'value': item['value'][0]
        }
        for item in timeline
    ])
    print(df.head())
else:
    print("Không thể lấy dữ liệu, status code:", response.status_code)


await httpx.AsyncClient().get("https://trends.google.com.vn/trends/explore?geo=VN&q=truy%E1%BB%87n%20ch%E1%BB%AF&hl=vi")