import { isPlatformServer } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Genre } from '@schemas/Genre';
import { Novel } from '@schemas/Novel';
import { NovelList } from '@schemas/NovelList';
import { NovelTopView } from '@schemas/NovelTopView';
import { IServiceResponse } from '@schemas/ResponseType';
import { NovelService } from '@services/novel.service';
import { SeoService } from '@services/seo.service';
import globalConfig from 'globalConfig';
import { ISSRComponent } from 'src/app/core/interface';

@Component({
  selector: 'app-home',
  standalone: false,
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HomeComponent implements OnInit, ISSRComponent {
  recommendNovels: Novel[] = [];
  carouselNovels: Novel[] = [];
  completeNovels: Novel[] = [];
  TopNovels?: NovelTopView;
  selectedGenre: string | null = null;
  carouselIndex = 0;
  listGenres: Genre[] = [];
  options: any[] = [];
  constructor(private novelService: NovelService, private router: Router,
    private seoService: SeoService,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Object
  ) { 

  }
  get isServer(): boolean {
    return isPlatformServer(this.platformId);
  }

  ngOnInit(): void {
    if (!this.isServer) {

      this.fetchTopNovels();
      this.fetchCompleteNovels();
    }

    this.route.data.subscribe(({ recommendRes }) => {
      if (recommendRes.status == 0) return;
      this.carouselNovels = recommendRes.data.slice(0, 8);      
      this.recommendNovels = recommendRes.data.slice(8, 20);
      this.SetupSeo();
      
      this.cd.markForCheck();
    });



    this.novelService.getGenres().subscribe(res => {
      this.listGenres = res

      this.options = [{ label: 'Tất cả', value: -1 },
      ...this.listGenres.map((genre) => {
        return { 'label': genre.title, 'value': genre.id }
      })
      ]
      this.cd.markForCheck();
    })

  }

  // filteredNovels() {
  //   return this.novels.filter(
  //     (novel) =>
  //       novel.title.toLowerCase().includes(this.searchTerm.toLowerCase()) &&
  //       (!this.selectedGenre || novel.genres.includes(this.selectedGenre))
  //   );
  // }
  fetchTopNovels() {
    this.novelService.getTopNovels().subscribe((res: IServiceResponse<NovelTopView>) => {
      if (res.data)
        this.TopNovels = res.data;
      this.cd.markForCheck();
    });
  }

  fetchCompleteNovels(genre: string = '-1') {
    this.novelService.getNovels({ page: '0', step: '12', status: '1', sort: '1', genre: genre }).subscribe((res: IServiceResponse<NovelList>) => {
      if (res.data)
        this.completeNovels = res.data?.novels;
      this.cd.markForCheck();

    });
  }

  goToDetail(slug: string) {
    this.router.navigate(['/novel/detail', slug]);
  }

  SetupSeo() {
    const title = `Đọc Truyện Chữ Online Miễn Phí | Kho Truyện Hay Nhất Việt Nam`;
    const description = 'Đọc truyện chữ online miễn phí với hơn 50.000+ truyện ngôn tình, tiên hiệp, kiếm hiệp, đô thị, huyền huyễn hay nhất. Cập nhật 24/7, không quảng cáo, đọc mượt mà trên mọi thiết bị.';

    const seoData = {
      title,
      description,
      type: 'website' as const,
      image: `${globalConfig.BASE_URL}/logo.png`,
      url: globalConfig.BASE_URL,
      siteName: globalConfig.APP_NAME,
      locale: 'vi_VN',
      twitterCard: 'summary_large_image' as const,
      keywords: 'đọc truyện chữ online, truyện chữ miễn phí, ngôn tình hay, tiên hiệp full, kiếm hiệp hot, truyện xuyên không, đô thị huyền huyễn, truyện audio, saytruyenhot, kho truyện lớn, cập nhật hàng ngày, truyện hay nhất, truyện full hoàn thành, đọc truyện không quảng cáo',
      author: globalConfig.APP_NAME,
      canonical: globalConfig.BASE_URL,
      noindex: false,
      nofollow: false
    };

    this.seoService.setSEOData(seoData);

    // Add structured data schemas
    const schemas = [
      this.generateHomePageSchema(),
      this.generateWebsiteSearchSchema(),
      this.generateBreadcrumbSchema()
    ];

    this.seoService.addStructuredData(schemas);

    // Preload critical resources
    this.preloadCriticalResources();
  }

  /**
   * Generate homepage specific schema
   */
  private generateHomePageSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      'name': `Trang chủ - ${globalConfig.APP_NAME}`,
      'description': 'Trang chủ của website đọc truyện chữ online hàng đầu Việt Nam',
      'url': globalConfig.BASE_URL,
      'mainEntity': {
        '@type': 'ItemList',
        'name': 'Truyện đề xuất',
        'description': 'Danh sách truyện được đề xuất và phổ biến nhất',
        'numberOfItems': this.recommendNovels.length,
        'itemListElement': this.recommendNovels.slice(0, 10).map((novel, index) => ({
          '@type': 'ListItem',
          'position': index + 1,
          'item': {
            '@type': 'Book',
            'name': novel.title,
            'author': novel.author,
            'url': `${globalConfig.BASE_URL}/truyen/${novel.url}-${novel.id}`,
            'image': novel.coverImage
          }
        }))
      },
      'breadcrumb': {
        '@type': 'BreadcrumbList',
        'itemListElement': [{
          '@type': 'ListItem',
          'position': 1,
          'name': 'Trang chủ',
          'item': globalConfig.BASE_URL
        }]
      }
    };
  }

  onCompleteNovelsChange($event: any) {
    this.fetchCompleteNovels(String($event))
  }

  /**
   * Generate website search schema
   */
  private generateWebsiteSearchSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      'url': globalConfig.BASE_URL,
      'potentialAction': {
        '@type': 'SearchAction',
        'target': {
          '@type': 'EntryPoint',
          'urlTemplate': `${globalConfig.BASE_URL}/tim-kiem?q={search_term_string}`
        },
        'query-input': 'required name=search_term_string'
      }
    };
  }

  /**
   * Generate breadcrumb schema for homepage
   */
  private generateBreadcrumbSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': [{
        '@type': 'ListItem',
        'position': 1,
        'name': 'Trang chủ',
        'item': globalConfig.BASE_URL
      }]
    };
  }

  /**
   * Preload critical resources for better performance
   */
  private preloadCriticalResources(): void {
    // Preload hero images if available
    if (this.TopNovels?.dailyNovels && this.TopNovels.dailyNovels.length > 0) {
      const heroImages = this.TopNovels.dailyNovels.slice(0, 3).map((novel: any) => novel.coverImage).filter(Boolean);
      this.seoService.addCriticalPreloads(
        heroImages.map((url: string) => ({ url, as: 'image', type: 'image/jpeg' }))
      );
    }
  }
}
