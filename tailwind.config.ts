
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'selector',
  content: ["./src/**/*.{html,ts}"], // Ensure Tailwind scans your files
  theme: {
    container: {
      type: "inline-size", // <PERSON><PERSON><PERSON> buộ<PERSON> đ<PERSON> xử lý container query
    },
    screens: {
      xs: '480px',
      sm: '640px',
      md: '768px',
      lg: '940px',
      xl: '1100px',
      // '2xl': '1200px',
    },
    extend: {
      boxShadow: {
        common: '0px 2px 6px 1px rgb(95, 153, 174, 0.2)',
        book: `
        rgb(0, 0, 0, 0.55) 8px 0px 10px 0px inset,
        rgb(0, 0, 0, 0.3) 0px -1px 5px 0px inset,
        -12px 12px 8px rgba(0, 0, 0, 0.3)
      `,
      },
      colors: {
        primary: {
          50: "var(--color-primary-100)",
          100: "var(--color-primary-100)",
          200: "var(--color-primary-200)",
          300: "var(--color-primary-200)",
        },
        background:
        {
          100: "var(--color-background-100)",
          200: "var(--color-background-200)",
          300: "var(--color-background-300)",
        },

        secondary: {
          50: '#00c7f9',
          100: '#009ece',
          200: '#0077a5',
          300: '#00537e',
          400: '#003059',
        },
        dark: {
          "background": "#191A1C",
          50: "#D9D9D9",
          100: "#CFCFCF",
          150: "#B9B9B9",
          200: "#A0A0A0",
          250: "#8C8C8C",
          300: "#7C7C7C",
          350: "#6B6B6B",
          400: "#626262",
          450: "#545454",
          500: "#4A4A4A",
          550: "#3D3D3D",
          600: "#383838",
          650: "#2F2F2F",
          700: "#2A2A2A",
          750: "#212121",
          800: "#1E1E1E",
          850: "#171717",
          900: "#121212",
          950: "#0A0A0A",

        },
        "light-text": "#000000",
        "dark-text": "#f5f5f5 ",
      },
    },
  },
  plugins: [require('@tailwindcss/container-queries')],
};
