import { isPlatformServer } from '@angular/common';
import {
  AfterViewInit,
  ComponentRef,
  Directive,
  ElementRef,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  OnDestroy,
  Output,
  PLATFORM_ID,
  Renderer2,
} from '@angular/core';
import { TutorialComponent } from '@components/tutorial/tutorial.component';
import { DynamicLoadingService } from '@services/dynamic.loading.service';
import { TutorialService, TutorialStatus } from '@services/tutorial.service';

@Directive({
  selector: '[appTutorial]',
})
export class TutorialDirective implements AfterViewInit, OnDestroy {
  private overlay?: HTMLElement;
  private glow?: HTMLElement;
  @Output()
  public onClick = new EventEmitter<void>();

  @Input() group: string = "setting"
  @Input() order = 0
  @Input() needClick = false
  isStart = false
  tutorialRef?: ComponentRef<TutorialComponent>
  constructor(private el: ElementRef,
    private renderer: Renderer2,
    private toturialService: TutorialService,
    private dynamicLoadingService: DynamicLoadingService,
    @Inject(PLATFORM_ID) private platformId: Object) { }

  ngAfterViewInit() {
    if (isPlatformServer(this.platformId)) return
    this.toturialService.setToturial(this.group.toString(), { order: this.order, title: '', content: '' });
    this.toturialService.On(this.group, this.order)?.subscribe((status) => {
      if (status == TutorialStatus.Start) {
        this.start();

      }
      if (status == TutorialStatus.Done) {
        this.stop();
      }

    });





  }

  @HostListener('click', ['$event']) onClickEvent(event: MouseEvent) {
    if (this.isStart) {
      this.toturialService.Next(this.group);
    }
  }


  start() {
    this.tutorialRef = this.dynamicLoadingService.createDynamicComponent<TutorialComponent>(TutorialComponent)

    this.isStart = true;
    let rect = this.el.nativeElement.getBoundingClientRect();
    let x = rect.left + rect.width / 2;
    let y = rect.top + rect.height / 2;
    this.tutorialRef?.instance.setPosition(x, y);
    this.tutorialRef?.instance.next.subscribe(() => {
      this.toturialService.Next(this.group);

    })
    this.createOverlay();
    this.highlightElement();

  }

  private createOverlay() {
    this.overlay = this.renderer.createElement('div');
    this.renderer.setStyle(this.overlay, 'position', 'fixed');
    this.renderer.addClass(this.overlay, 'inset-0');
    this.renderer.addClass(this.overlay, 'w-screen');
    this.renderer.addClass(this.overlay, 'h-screen');
    this.renderer.addClass(this.overlay, 'bottom-0');
    this.renderer.setStyle(this.overlay, 'backgroundColor', 'rgba(0, 0, 0, 0.7)');
    this.renderer.setStyle(this.overlay, 'zIndex', '9998');
    this.renderer.appendChild(this.el.nativeElement.parentElement, this.overlay);
  }

  private highlightElement() {

    const host = this.el.nativeElement;
    this.renderer.setStyle(host, 'position', 'relative');
    this.renderer.setStyle(host, 'zIndex', '9999');
    this.renderer.addClass(host, 'outline',);
    this.renderer.addClass(host, 'outline-5',);
    if (this.needClick) {
      // Optional: glow effect
      this.glow = this.renderer.createElement('div');
      this.renderer.addClass(this.glow, 'size-10');
      this.renderer.addClass(this.glow, 'bg-yellow-100');
      this.renderer.addClass(this.glow, 'absolute');
      // this.renderer.addClass(this.glow, '-transform-x-1/2');
      // this.renderer.addClass(this.glow, 'right-1/2');
      this.renderer.addClass(this.glow, 'top-0');
      this.renderer.addClass(this.glow, 'left-0');
      this.renderer.addClass(this.glow, 'h-full');
      this.renderer.addClass(this.glow, 'w-full');
      this.renderer.addClass(this.glow, 'animate-ping');
      this.renderer.addClass(this.glow, 'rounded-full');
      this.renderer.setStyle(this.glow, 'pointerEvents', 'none');
      this.renderer.appendChild(host, this.glow);
    }
    else {
      this.renderer.setStyle(host, 'pointerEvents', 'none');
    }
  }

  public stop() {
    if (!this.isStart) return
    const host = this.el.nativeElement;
    this.renderer.removeStyle(host, 'zIndex');
    this.renderer.removeStyle(host, 'relative');
    if (this.needClick) {
      this.renderer.removeChild(this.el.nativeElement, this.glow);
    }
    else {
      this.renderer.removeStyle(host, 'pointerEvents');
    }
    this.renderer.removeChild(this.el.nativeElement.parentElement, this.overlay);
    this.isStart = false

  }

  ngOnDestroy() {
    this.stop();
  }
}
