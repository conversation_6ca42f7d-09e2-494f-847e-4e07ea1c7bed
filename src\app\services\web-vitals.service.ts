import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

interface WebVitalMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
}

@Injectable({
  providedIn: 'root'
})
export class WebVitalsService {
  private metrics: Map<string, WebVitalMetric> = new Map();

  constructor(@Inject(PLATFORM_ID) private platformId: Object) {
    if (isPlatformBrowser(this.platformId)) {
      this.initWebVitals();
    }
  }

  private async initWebVitals() {
    try {
      // Dynamic import for web-vitals library
      const { getCLS, getFID, getFCP, getLCP, getTTFB } = await import('web-vitals');
      
      // Collect Core Web Vitals
      getCLS(this.onMetric.bind(this));
      getFID(this.onMetric.bind(this));
      getFCP(this.onMetric.bind(this));
      getLCP(this.onMetric.bind(this));
      getTTFB(this.onMetric.bind(this));
      
    } catch (error) {
      console.warn('Web Vitals library not available:', error);
    }
  }

  private onMetric(metric: WebVitalMetric) {
    this.metrics.set(metric.name, metric);
    
    // Send to analytics
    this.sendToAnalytics(metric);
    
    // Log for development
    if (!this.isProduction()) {
      console.log(`${metric.name}: ${metric.value} (${metric.rating})`);
    }
  }

  private sendToAnalytics(metric: WebVitalMetric) {
    // Send to Google Analytics 4
    if (typeof (window as any).gtag !== 'undefined') {
      (window as any).gtag('event', metric.name, {
        event_category: 'Web Vitals',
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        custom_parameter_1: metric.rating,
        non_interaction: true,
      });
    }

    // Send to custom analytics endpoint if needed
    this.sendToCustomAnalytics(metric);
  }

  private sendToCustomAnalytics(metric: WebVitalMetric) {
    // Optional: Send to your own analytics endpoint
    fetch('/api/web-vitals', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        metric: metric.name,
        value: metric.value,
        rating: metric.rating,
        url: window.location.href,
        timestamp: Date.now()
      })
    }).catch(() => {
      // Silently fail - don't block user experience
    });
  }

  private isProduction(): boolean {
    return (window as any).location?.hostname === 'saytruyenhot.com';
  }

  // Public methods for getting metrics
  getMetric(name: string): WebVitalMetric | undefined {
    return this.metrics.get(name);
  }

  getAllMetrics(): WebVitalMetric[] {
    return Array.from(this.metrics.values());
  }

  // Performance optimization helpers
  preloadCriticalResources(resources: string[]) {
    if (isPlatformBrowser(this.platformId)) {
      resources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource;
        link.as = this.getResourceType(resource);
        document.head.appendChild(link);
      });
    }
  }

  private getResourceType(url: string): string {
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) return 'image';
    if (url.match(/\.(css)$/i)) return 'style';
    if (url.match(/\.(js)$/i)) return 'script';
    if (url.match(/\.(woff|woff2|ttf|otf)$/i)) return 'font';
    return 'fetch';
  }

  // Lazy loading optimization
  observeImages() {
    if (isPlatformBrowser(this.platformId) && 'IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset['src']) {
              img.src = img.dataset['src'];
              img.removeAttribute('data-src');
              imageObserver.unobserve(img);
            }
          }
        });
      });

      // Observe all images with data-src
      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }
  }
}
