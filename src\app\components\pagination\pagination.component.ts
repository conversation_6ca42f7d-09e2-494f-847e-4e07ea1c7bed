import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  ViewChild,
  ViewEncapsulation,
  SimpleChanges,
  ChangeDetectorRef,
  HostListener,
  OnDestroy
} from '@angular/core';
import { ActivatedRoute, Params, RouterModule } from '@angular/router';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-pagination',
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, RouterModule],
  encapsulation: ViewEncapsulation.None,
  animations: [
    trigger('slideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-10px)' }),
        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ]),
      transition(':leave', [
        animate('200ms ease-in', style({ opacity: 0, transform: 'translateY(-10px)' }))
      ])
    ])
  ]
})
export class PaginationComponent implements OnChanges, OnDestroy {
  pages: any[] = [];
  showSearch = false;
  
  @Input() rootLink = '';
  @Input() queryParams: Params = {};
  @Input() currentPage = 1;
  @Input() totalpage!: number;
  @Output() OnChange = new EventEmitter<number>();
  
  @ViewChild('searchInput') searchInputElement!: ElementRef<HTMLInputElement>;

  private readonly PAGE_DISPLAY_THRESHOLD = 3;
  private readonly HALF_DISPLAY = 1;
  private searchTimeout: any;

  constructor(
    public route: ActivatedRoute,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['totalpage'] || changes['currentPage']) {
      if (this.totalpage > 0) {
        this.pageSetup(Number(this.currentPage));
      }
    }
  }

  ngOnDestroy(): void {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  /**
   * Global keyboard shortcuts
   */
  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent): void {
    // Only handle if pagination is visible and search is not active
    if (this.showSearch) return;
    
    switch (event.key) {
      case 'ArrowLeft':
        if (this.currentPage > 1) {
          event.preventDefault();
          this.navigateToPage(this.currentPage - 1);
        }
        break;
      case 'ArrowRight':
        if (this.currentPage < this.totalpage) {
          event.preventDefault();
          this.navigateToPage(this.currentPage + 1);
        }
        break;
      case '/':
        event.preventDefault();
        this.toggleSearch();
        break;
    }
  }

  /**
   * Setup pagination pages with optimized logic
   */
  private pageSetup(page: number): void {
    this.pages = [];

    if (this.totalpage <= this.PAGE_DISPLAY_THRESHOLD) {
      // Show all pages if total is 5 or less
      for (let index = 1; index <= this.totalpage; index++) {
        this.pages.push({ title: index.toString(), link: "trang-" + index });
      }
      return;
    }

    if (page <= 3) {
      // Show first few pages
      const endPage = Math.max(page + this.HALF_DISPLAY, 3);
      for (let index = 1; index <= endPage; index++) {
        this.pages.push({ title: index.toString(), link: "trang-" + index });
      }
      this.pages.push({ title: '...', link: '' }, { title: this.totalpage.toString(), link: "trang-" + this.totalpage });
    } else if (page <= this.totalpage - 3) {
      // Show middle pages
      this.pages.push({ title: '1', link: "trang-1" }, { title: '...', link: '' });
      for (let index = page - 1; index <= page + 1; index++) {
        this.pages.push({ title: index.toString(), link: "trang-" + index });
      }
      this.pages.push({ title: '...', link: '' }, { title: this.totalpage.toString(), link: "trang-" + this.totalpage });
    } else {
      // Show last few pages
      this.pages.push({ title: '1', link: "trang-1" }, { title: '...', link: '' });
      const startPage = Math.min(this.totalpage - 3, page);
      for (let index = startPage; index <= this.totalpage; index++) {
        this.pages.push({ title: index.toString(), link: "trang-" + index });
      }
    }
  }

  /**
   * Navigate to a specific page (for internal use)
   */
  private navigateToPage(pageNumber: number): void {
    if (pageNumber !== this.currentPage && pageNumber >= 1 && pageNumber <= this.totalpage) {
      this.currentPage = pageNumber;
      this.pageSetup(pageNumber);
      this.OnChange.emit(pageNumber);
      this.cdr.markForCheck();
    }
  }

  /**
   * Toggle search input visibility with animation
   */
  toggleSearch(): void {
    this.showSearch = !this.showSearch;
    this.cdr.markForCheck();
    
    if (this.showSearch) {
      // Focus input after animation
      setTimeout(() => {
        if (this.searchInputElement?.nativeElement) {
          this.searchInputElement.nativeElement.focus();
          this.searchInputElement.nativeElement.select();
        }
      }, 300);
    }
  }

  /**
   * Navigate to specific page with validation
   */
  goToPage(pageValue: string): void {
    if (!pageValue || pageValue.trim() === '') {
      return;
    }

    const pageNumber = parseInt(pageValue.trim(), 10);
    
    if (isNaN(pageNumber) || pageNumber < 1 || pageNumber > this.totalpage) {
      return;
    }

    if (pageNumber !== this.currentPage) {
      this.navigateToPage(pageNumber);
      this.showSearch = false;
    }
  }

  /**
   * Handle search input blur with delay
   */
  onSearchBlur(): void {
    // Delay hiding to allow button click
    this.searchTimeout = setTimeout(() => {
      this.showSearch = false;
      this.cdr.markForCheck();
    }, 150);
  }

  /**
   * Validate search input on change
   */
  validateInput(input: HTMLInputElement): void {
    const value = parseInt(input.value, 10);
    
    // Clear any existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    
    // Remove invalid characters and ensure within bounds
    if (input.value && (isNaN(value) || value < 1)) {
      input.value = '1';
    } else if (value > this.totalpage) {
      input.value = this.totalpage.toString();
    }
  }

  /**
   * Check if page input is valid
   */
  isValidPageInput(value: string): boolean {
    if (!value || value.trim() === '') {
      return false;
    }
    
    const pageNumber = parseInt(value.trim(), 10);
    return !isNaN(pageNumber) && pageNumber >= 1 && pageNumber <= this.totalpage;
  }

  /**
   * Legacy method for backward compatibility
   */
  OnChangePage(page: string): void {
    this.goToPage(page);
  }

  /**
   * Handle focus state
   */
  onFocus(value: boolean): void {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
    
    if (!value) {
      this.showSearch = false;
      this.cdr.markForCheck();
    }
  }

  /**
   * TrackBy function for better performance
   */
  trackByPage(index: number, page: { title: string; link: string }): string {
    return page.title + page.link;
  }
}
