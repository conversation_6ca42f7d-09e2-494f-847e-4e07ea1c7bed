const { Pool } = require("pg");

class DataSource {
  constructor(
    host = "localhost",
    port = 5432,
    user = "postgres",
    password = "",
    database = "your_database_name"
  ) {
    this.pool = new Pool({
      host,
      port,
      user,
      password,
      database,
    });
  }

  async allNovels() {
    const query = "SELECT id, url FROM Novel";
    const { rows } = await this.pool.query(query);
    return rows;
  }

  async allChapters(fields = "*", offset = 0, limit = 10000) {
    const query = `SELECT ${fields} FROM chapter ORDER BY id LIMIT $1 OFFSET $2`;
    const values = [limit, offset];
    const { rows } = await this.pool.query(query, values);
    return rows;
  }

  async close() {
    await this.pool.end();
  }
}
module.exports = { DataSource };
