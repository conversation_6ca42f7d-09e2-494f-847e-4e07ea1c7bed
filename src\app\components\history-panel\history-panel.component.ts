import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ViewEncapsulation } from '@angular/core';
import { RouterModule } from '@angular/router';
import { EmptyComponent } from '@components/empty/empty.component';
import { PanelComponent } from '@components/panel/panel.component';
import { Novel } from '@schemas/Novel';
import { HistoryService } from '@services/history.service';
import { NovelService } from '@services/novel.service';
import { PinesModule } from 'src/app/shared/pines/pines.module';

@Component({
  selector: 'app-history-panel',
  standalone: true,
  templateUrl: './history-panel.component.html',
  styleUrl: './history-panel.component.scss',
  imports: [CommonModule, PanelComponent, PinesModule, RouterModule, EmptyComponent],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush 
})
export class HistoryPanelComponent {
  novels: Novel[] = [];
  constructor(private novelService: NovelService, private historyService: HistoryService) {

  }
  ngOnInit() {
    this.novels = this.historyService.GetHistorys();
  }

  ngOnChanges() {
    
  }
}
