import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChild,
  EventEmitter,
  Inject,
  Input,
  NgZone,
  Output,
  PLATFORM_ID,
  TemplateRef,
} from '@angular/core';
import { interval, Subscription } from 'rxjs';

@Component({
  imports: [CommonModule],
  standalone: true,
  selector: 'app-carousel',
  templateUrl: './carousel.component.html',
  styleUrls: ['./carousel.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CarouselComponent {
  @Input() items: any[] = [];
  @Input() autoPlay: boolean = true;
  @Input() debound: number = 500;
  @Input() delay: number = 5000;
  @Input() index: number = 0;
  @Output() indexChange = new EventEmitter<number>();
  @ContentChild('maincontent', { static: true })
  itemTemplate?: TemplateRef<any>;
  @ContentChild('navcontent', { static: true }) navTemplate?: TemplateRef<any>;

  subscription?: Subscription
  attrs: any[] = [];
  nextTime = 0;
  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private cd: ChangeDetectorRef,
    private ngZone: NgZone,
  ) { }
  ngAfterViewInit() {
    // this.Refresh();
    this.index = 0;
  }

  ngOnInit() {
    if (this.autoPlay && isPlatformBrowser(this.platformId)) {
      this.ngZone.runOutsideAngular(() => {
        this.subscription = interval(this.delay).subscribe(() => {
          this.prev();
        });
      });
    }
  }
  refresh(next: boolean = true) {
    const halfLength = this.floor(this.items.length / 2);
    this.attrs = Array.from({ length: this.items.length }, (_, i) => {
      let idx = ((i + this.index) % this.items.length);
      let index = idx > halfLength ? (idx - this.items.length) : idx;
      return {
        hidden: !(index == 0 || index == 1 || index == -1),
        transform: 'translateX(' + index * 100 + '%)',
        'animation': next ? (index == 0 || index == 1) : (index == 0 || index == -1),
      };
    });
    this.indexChange.emit(this.index);
    this.cd.detectChanges();
  }
  ngOnChanges() {
    this.refresh();
  }
  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }
  next() {
    if (Date.now() < this.nextTime) return;
    this.nextTime = Date.now() + this.debound;
    this.index = (this.index + 1) % this.items.length;

    this.refresh(true);
  }

  prev() {
    if (Date.now() < this.nextTime) return;
    this.nextTime = Date.now() + this.debound;
    this.index = (this.index - 1 + this.items.length) % this.items.length;
    this.refresh(false);
  }
  floor(n: number) {
    return Math.floor(n);
  }
}
