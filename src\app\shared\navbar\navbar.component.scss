
.darkmode-icon
{
    @apply hidden;
}

.lightmode-icon
{
    @apply block;
}

.dark-mode
{
    .darkmode-icon
    {
        @apply block;
    }
    .lightmode-icon
    {
        @apply hidden;
    }
}


.user-popup
{
  @apply origin-top transition-opacity absolute flex flex-col gap-2 top-[56px] right-0 bg-slate-50 dark:bg-dark-700 shadow-2xl p-4 space-y-1 w-80 rounded-lg;
    &::before
    {
        @apply content-[''] absolute -top-[10px] right-16 xl:right-0 transform translate-x-[-50%] border-b-[10px] border-white dark:border-neutral-700;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
    }
}
.list-popup
{
  @apply origin-top transition-opacity absolute flex flex-col gap-2 top-[56px] rounded-lg right-0 bg-slate-50 dark:bg-dark-700 shadow-2xl p-3 space-y-1 z-[100] rounded-b;
  &::before
  {
      @apply content-[''] absolute -top-[10px] right-2 transform translate-x-[-50%] border-b-[10px] border-white dark:border-neutral-700;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
  }
}


.novel-header::before {
    content: "";
    position: absolute;
    top: 0px;
    left: 0;
    width: 100%;
    height: 72px;
    z-index: 1;
    // background: linear-gradient(0deg, #000000 0%, #ffffff2b 5%, #ffffff00 80%, #3c3c3c 100%);
    @apply bg-gradient-to-b from-dark-background/60 dark:to-100% to-dark-background/[5%];
}





#menu-popup-checkbox {
    @apply hidden;
  }
  
  .menu-popup-toggle {
    @apply relative size-6 flex flex-col items-center justify-center gap-1.5 cursor-pointer transition duration-500;
  }
  
  .bars {
    @apply w-full h-0.5 bg-slate-100 rounded-lg;
  }
  
  #bar1, #bar3 {
    @apply w-[80%];
  }
  
  #bar2 {
    @apply transition duration-200;
  }
  
  #menu-popup-checkbox:checked + .menu-popup-toggle {
    @apply rotate-180 transition duration-200;
  }
  
  #menu-popup-checkbox:checked + .menu-popup-toggle .bars {
    @apply absolute transition duration-200;
  }
  
  #menu-popup-checkbox:checked + .menu-popup-toggle #bar2 {
    @apply scale-x-0 transition duration-200;
  }
  
  #menu-popup-checkbox:checked + .menu-popup-toggle #bar1 {
    @apply w-full rotate-45 transition duration-200;
  }
  
  #menu-popup-checkbox:checked + .menu-popup-toggle #bar3 {
    @apply w-full -rotate-45 transition duration-200;
  }
  