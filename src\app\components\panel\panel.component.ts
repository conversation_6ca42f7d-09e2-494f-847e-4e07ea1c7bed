import { CommonModule } from '@angular/common';
import { Component, ContentChild, ElementRef, Input, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'app-panel',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './panel.component.html',
  styleUrl: './panel.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class PanelComponent {
  @Input() title = '';
  @ContentChild('iconTemplate') iconTemplate: TemplateRef<HTMLElement> | null = null;
  @ViewChild('paneltitle') paneltitle: ElementRef<HTMLElement> | null = null;
  ngOnInit() {
  }
  ngAfterViewInit() {
  }
}
