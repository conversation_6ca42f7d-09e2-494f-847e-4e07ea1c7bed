import { animate, state, style, transition, trigger } from '@angular/animations';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
  ApplicationRef,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ContentChild,
  EventEmitter,
  Inject,
  Input,
  NgZone,
  Output,
  PLATFORM_ID,
  TemplateRef
} from '@angular/core';
import { delay, interval, Subscription } from 'rxjs';

@Component({
  imports: [CommonModule],
  standalone: true,
  selector: 'app-swipper-fade',
  templateUrl: './swipper-fade.component.html',
  styleUrls: ['./swipper-fade.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('fadeMain', [
      state('visible', style({
        opacity: 1,
        transform: 'translateX(0)'
      })),
      state('hidden', style({
        opacity: 0,
        transform: 'translateX(-25px)'
      })),
      transition('hidden => visible', [
        animate('400ms ease-out')
      ]),
      transition('visible => hidden', [
        animate('10ms ease-in')
      ])
    ]),
    trigger('fadeBg', [
      state('visible', style({
        opacity: 1,
        transform: 'translateX(0) scaleX(110%)',
      })),
      state('hidden', style({
        opacity: 0,
        transform: 'translateX(5%) scaleX(110%)',
      })),
      transition('hidden => visible', [
        animate('400ms ease-out')
      ]),
      transition('visible => hidden', [
        animate('400ms ease-in')
      ])
    ])

  ]
})
export class SwipperFadeComponent {
  @Input() items: any[] = [];
  @Input() autoPlay: boolean = true;
  @Input() debound: number = 500;
  @Input() delay: number = 5000;
  @Input() duration: number = 5000;
  @Input() index: number = 0;
  @Output() indexChange = new EventEmitter<number>();
  @ContentChild('bgcontent', { static: true })
  bgTemplate?: TemplateRef<any>;
  @ContentChild('maincontent', { static: true })
  itemTemplate?: TemplateRef<any>;
  @ContentChild('navcontent', { static: true }) navTemplate?: TemplateRef<any>;

  subscription?: Subscription;
  attrs: any[] = [];
  nextTime = 0;
  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private cd: ChangeDetectorRef,
    private applicationRef: ApplicationRef,
    private ngZone: NgZone
  ) { }
  ngAfterViewInit() {
    // this.Refresh();
    this.index = 0;
  }

  ngOnInit() {

    if (this.autoPlay && isPlatformBrowser(this.platformId)) {
      this.ngZone.runOutsideAngular(() => {
        this.subscription = interval(this.duration)
          .pipe(delay(this.delay))
          .subscribe(() => {
            this.next();
          }
          );
      });
    }
  }
  Refresh(next: boolean = true) {
    this.attrs = Array.from({ length: this.items.length }, (_, i) => {
      return {
        hidden: i != this.index,
      };
    });
    this.cd.detectChanges();
    this.indexChange.emit(this.index);
  }
  ngOnChanges() {
    this.Refresh();
  }
  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }
  next() {
    if (Date.now() < this.nextTime) return;
    this.nextTime = Date.now() + this.debound;
    this.index = (this.index + 1) % this.items.length;

    this.Refresh(true);
  }

  prev() {
    if (Date.now() < this.nextTime) return;
    this.nextTime = Date.now() + this.debound;
    this.index = (this.index - 1 + this.items.length) % this.items.length;
    this.Refresh(false);
  }
  floor(n: number) {
    return Math.floor(n);
  }
}
