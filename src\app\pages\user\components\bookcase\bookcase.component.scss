.bookcase-container {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

// Header
.bookcase-header {
  margin-bottom: 2rem;
  text-align: center;
}

.bookcase-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.dark .bookcase-title {
  color: #f9fafb;
}

.bookcase-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
}

.dark .bookcase-subtitle {
  color: #9ca3af;
}

// Tab Navigation
.tab-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid #e5e7eb;
}

.dark .tab-navigation {
  border-bottom-color: #374151;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #f97316;
  background: rgba(249, 115, 22, 0.05);
}

.tab-button.active {
  color: #f97316;
  border-bottom-color: #f97316;
  background: rgba(249, 115, 22, 0.05);
}

.dark .tab-button {
  color: #9ca3af;
}

.dark .tab-button:hover,
.dark .tab-button.active {
  color: #fb923c;
  background: rgba(249, 115, 22, 0.1);
}

// Tab Content
.tab-content {
  min-height: 400px;
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #6b7280;
}

.dark .empty-state {
  color: #9ca3af;
}

.empty-icon {
  width: 4rem;
  height: 4rem;
  margin-bottom: 1rem;
  color: #d1d5db;
}

.dark .empty-icon {
  color: #4b5563;
}

.empty-state h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #374151;
}

.dark .empty-state h3 {
  color: #d1d5db;
}

.empty-state p {
  font-size: 0.875rem;
  max-width: 20rem;
}

// Novel Grid (Favorites)
.novel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.novel-card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 6px 1px rgba(95, 153, 174, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.novel-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px 2px rgba(95, 153, 174, 0.25);
}

.dark .novel-card {
  background: #262626;
  color: #e5e7eb;
}

.novel-cover {
  position: relative;
  aspect-ratio: 3/4;
  overflow: hidden;
}

.novel-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.novel-card:hover .novel-cover img {
  transform: scale(1.05);
}

.novel-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.novel-card:hover .novel-actions {
  opacity: 1;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-btn {
  background: rgba(239, 68, 68, 0.9);
  color: white;
}

.remove-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.novel-info {
  padding: 1rem;
}

.novel-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dark .novel-title {
  color: #f3f4f6;
}

.novel-author {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.75rem;
}

.dark .novel-author {
  color: #9ca3af;
}

.reading-progress {
  margin-bottom: 0.75rem;
}

.progress-bar {
  width: 100%;
  height: 0.5rem;
  background: #e5e7eb;
  border-radius: 0.25rem;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.dark .progress-bar {
  background: #374151;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f97316, #ea580c);
  border-radius: 0.25rem;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.dark .progress-text {
  color: #9ca3af;
}

.last-read {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.continue-btn {
  width: 100%;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.continue-btn:hover {
  background: linear-gradient(135deg, #ea580c, #dc2626);
  transform: translateY(-1px);
}

.continue-btn.small {
  width: auto;
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

// History List
.history-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
}

.clear-history-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-history-btn:hover:not(:disabled) {
  background: #dc2626;
}

.clear-history-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 6px 1px rgba(95, 153, 174, 0.2);
  transition: all 0.3s ease;
}

.history-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px 2px rgba(95, 153, 174, 0.25);
}

.dark .history-item {
  background: #262626;
  color: #e5e7eb;
}

.history-cover {
  flex-shrink: 0;
  width: 4rem;
  height: 5rem;
  border-radius: 0.375rem;
  overflow: hidden;
}

.history-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.history-info {
  flex: 1;
  min-width: 0;
}

.history-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .history-title {
  color: #f3f4f6;
}

.history-author {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.dark .history-author {
  color: #9ca3af;
}

.history-chapter {
  font-size: 0.875rem;
  color: #374151;
  margin-bottom: 0.25rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .history-chapter {
  color: #d1d5db;
}

.history-time {
  font-size: 0.75rem;
  color: #9ca3af;
}

.history-actions {
  flex-shrink: 0;
}

// Responsive
@media (max-width: 640px) {
  .bookcase-container {
    padding: 1rem;
  }
  
  .tab-navigation {
    flex-direction: column;
    gap: 0;
  }
  
  .tab-button {
    justify-content: center;
    border-bottom: none;
    border-right: 2px solid transparent;
  }
  
  .tab-button.active {
    border-bottom: none;
    border-right-color: #f97316;
  }
  
  .novel-grid {
    grid-template-columns: 1fr;
  }
  
  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .history-cover {
    width: 3rem;
    height: 4rem;
  }
}
