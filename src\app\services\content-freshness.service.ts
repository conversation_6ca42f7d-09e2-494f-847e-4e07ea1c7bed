import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, interval } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { Novel } from '@schemas/Novel';
import { Chapter } from '@schemas/Chapter';
import globalConfig from 'globalConfig';

export interface ContentUpdate {
  type: 'novel' | 'chapter' | 'genre';
  id: number;
  title: string;
  updateTime: string;
  url: string;
}

export interface FreshnessMetrics {
  totalNovels: number;
  updatedToday: number;
  updatedThisWeek: number;
  newNovelsThisMonth: number;
  lastUpdateTime: string;
}

@Injectable({
  providedIn: 'root'
})
export class ContentFreshnessService {
  private recentUpdatesSubject = new BehaviorSubject<ContentUpdate[]>([]);
  private metricsSubject = new BehaviorSubject<FreshnessMetrics | null>(null);
  
  public recentUpdates$ = this.recentUpdatesSubject.asObservable();
  public metrics$ = this.metricsSubject.asObservable();

  constructor(
    private http: HttpClient,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    if (isPlatformBrowser(this.platformId)) {
      this.initializeContentTracking();
    }
  }

  private initializeContentTracking() {
    // Update metrics every 5 minutes
    interval(5 * 60 * 1000).pipe(
      switchMap(() => this.fetchContentMetrics()),
      catchError(error => {
        console.warn('Failed to fetch content metrics:', error);
        return [];
      })
    ).subscribe();

    // Initial load
    this.fetchContentMetrics().subscribe();
    this.fetchRecentUpdates().subscribe();
  }

  private fetchContentMetrics(): Observable<FreshnessMetrics> {
    return this.http.get<FreshnessMetrics>(`${globalConfig.BASE_API_URL}/content/metrics`);
  }

  private fetchRecentUpdates(): Observable<ContentUpdate[]> {
    return this.http.get<ContentUpdate[]>(`${globalConfig.BASE_API_URL}/content/recent-updates?limit=20`);
  }

  /**
   * Get content freshness indicators for SEO
   */
  getContentFreshnessIndicators(): {
    lastModified: string;
    updateFrequency: string;
    contentVolume: string;
  } {
    const metrics = this.metricsSubject.value;
    
    if (!metrics) {
      return {
        lastModified: new Date().toISOString(),
        updateFrequency: 'daily',
        contentVolume: '50000+'
      };
    }

    return {
      lastModified: metrics.lastUpdateTime,
      updateFrequency: this.calculateUpdateFrequency(metrics),
      contentVolume: this.formatContentVolume(metrics.totalNovels)
    };
  }

  private calculateUpdateFrequency(metrics: FreshnessMetrics): string {
    const dailyUpdates = metrics.updatedToday;
    
    if (dailyUpdates > 100) return 'hourly';
    if (dailyUpdates > 20) return 'daily';
    if (metrics.updatedThisWeek > 50) return 'weekly';
    return 'monthly';
  }

  private formatContentVolume(total: number): string {
    if (total >= 100000) return '100K+';
    if (total >= 50000) return '50K+';
    if (total >= 10000) return '10K+';
    if (total >= 1000) return '1K+';
    return total.toString();
  }

  /**
   * Generate structured data for content freshness
   */
  generateContentFreshnessSchema(): any {
    const indicators = this.getContentFreshnessIndicators();
    
    return {
      '@context': 'https://schema.org',
      '@type': 'DataCatalog',
      'name': `${globalConfig.APP_NAME} - Kho Truyện Chữ`,
      'description': `Kho truyện chữ online với ${indicators.contentVolume} bộ truyện, cập nhật ${indicators.updateFrequency}`,
      'url': globalConfig.BASE_URL,
      'dateModified': indicators.lastModified,
      'publisher': {
        '@type': 'Organization',
        'name': globalConfig.APP_NAME,
        'url': globalConfig.BASE_URL
      },
      'dataset': {
        '@type': 'Dataset',
        'name': 'Truyện chữ online',
        'description': `Bộ sưu tập ${indicators.contentVolume} truyện chữ đa thể loại`,
        'keywords': 'truyện chữ, ngôn tình, tiên hiệp, kiếm hiệp, đô thị, huyền huyễn',
        'temporalCoverage': '2020/..',
        'spatialCoverage': 'Vietnam',
        'inLanguage': 'vi'
      }
    };
  }

  /**
   * Get trending content for homepage
   */
  getTrendingContent(): Observable<{
    hotNovels: Novel[];
    newChapters: Chapter[];
    risingNovels: Novel[];
  }> {
    return this.http.get<{
      hotNovels: Novel[];
      newChapters: Chapter[];
      risingNovels: Novel[];
    }>(`${globalConfig.BASE_API_URL}/content/trending`);
  }

  /**
   * Generate content calendar for SEO
   */
  generateContentCalendar(): {
    dailyUpdates: string[];
    weeklyHighlights: string[];
    monthlyFeatures: string[];
  } {
    const now = new Date();
    const dayOfWeek = now.getDay();
    const dayOfMonth = now.getDate();

    return {
      dailyUpdates: this.getDailyContentPlan(dayOfWeek),
      weeklyHighlights: this.getWeeklyContentPlan(),
      monthlyFeatures: this.getMonthlyContentPlan(dayOfMonth)
    };
  }

  private getDailyContentPlan(dayOfWeek: number): string[] {
    const plans = [
      ['Chủ nhật: Top truyện tuần', 'Tổng hợp truyện hot nhất'],
      ['Thứ 2: Truyện mới', 'Giới thiệu truyện mới cập nhật'],
      ['Thứ 3: Ngôn tình hot', 'Spotlight thể loại ngôn tình'],
      ['Thứ 4: Tiên hiệp hay', 'Đặc sắc truyện tiên hiệp'],
      ['Thứ 5: Kiếm hiệp cổ điển', 'Truyện kiếm hiệp kinh điển'],
      ['Thứ 6: Đô thị hiện đại', 'Truyện đô thị thịnh hành'],
      ['Thứ 7: Huyền huyễn kỳ ảo', 'Thế giới huyền huyễn độc đáo']
    ];
    
    return plans[dayOfWeek] || plans[0];
  }

  private getWeeklyContentPlan(): string[] {
    return [
      'Top 10 truyện được đọc nhiều nhất tuần',
      'Truyện hoàn thành mới nhất',
      'Tác giả nổi bật của tuần',
      'Thể loại trending tuần này'
    ];
  }

  private getMonthlyContentPlan(dayOfMonth: number): string[] {
    const features = [
      'Truyện của tháng',
      'Tác giả xuất sắc tháng',
      'Thống kê đọc giả tháng',
      'Xu hướng thể loại tháng'
    ];
    
    return [features[Math.floor(dayOfMonth / 8)] || features[0]];
  }

  /**
   * Update content timestamp for SEO
   */
  updateContentTimestamp(contentType: string, contentId: number): void {
    if (isPlatformBrowser(this.platformId)) {
      const timestamp = new Date().toISOString();
      localStorage.setItem(`content_${contentType}_${contentId}_updated`, timestamp);
    }
  }

  /**
   * Get last content update time
   */
  getLastContentUpdate(contentType: string, contentId: number): string | null {
    if (isPlatformBrowser(this.platformId)) {
      return localStorage.getItem(`content_${contentType}_${contentId}_updated`);
    }
    return null;
  }

  /**
   * Generate sitemap priority based on content freshness
   */
  calculateSitemapPriority(lastUpdate: string, contentType: 'novel' | 'chapter' | 'genre'): number {
    const now = new Date();
    const updateDate = new Date(lastUpdate);
    const daysSinceUpdate = Math.floor((now.getTime() - updateDate.getTime()) / (1000 * 60 * 60 * 24));

    const basePriorities = {
      novel: 0.8,
      chapter: 0.6,
      genre: 0.7
    };

    let priority = basePriorities[contentType];

    // Boost priority for recent updates
    if (daysSinceUpdate <= 1) priority += 0.1;
    else if (daysSinceUpdate <= 7) priority += 0.05;
    else if (daysSinceUpdate <= 30) priority += 0.02;
    else if (daysSinceUpdate > 365) priority -= 0.1;

    return Math.max(0.1, Math.min(1.0, priority));
  }
}
