
// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #6b7280;
}

.dark .loading-container {
  color: #9ca3af;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #f97316;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.dark .loading-spinner {
  border-color: #374151;
  border-top-color: #fb923c;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Main Content
.user-page-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

// User Header
.user-header {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.dark .user-header {
  background: #1e293b;
  color: #e5e7eb;
}

@media (max-width: 768px) {
  .user-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
    padding: 1.5rem;
  }
}

.user-avatar {
  flex-shrink: 0;
}

.avatar-image {
  width: 6rem;
  height: 6rem;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #f97316;
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.dark .user-name {
  color: #f9fafb;
}

.user-email {
  color: #6b7280;
  font-size: 1rem;
  margin-bottom: 1rem;
}

.dark .user-email {
  color: #9ca3af;
}

.user-stats {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dark .stat-label {
  color: #9ca3af;
}

.stat-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1f2937;
}

.dark .stat-value {
  color: #f3f4f6;
}

.vip-status {
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
}

.vip-status.active {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  border-color: #f59e0b;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.dark .vip-status {
  background: #374151;
  border-color: #4b5563;
  color: #d1d5db;
}

.vip-icon {
  width: 1.25rem;
  height: 1.25rem;
}

// Tab Navigation
.tab-navigation {
  display: flex;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  margin-bottom: 2rem;
  gap: 0.5rem;
}

.dark .tab-navigation {
  background: #1e293b;
}

@media (max-width: 640px) {
  .tab-navigation {
    flex-direction: column;
    gap: 0.25rem;
  }
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  background: transparent;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  flex: 1;
  justify-content: center;
}

.tab-button:hover {
  background: rgba(249, 115, 22, 0.1);
  color: #f97316;
}

.tab-button.active {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  box-shadow: 0 2px 8px rgba(249, 115, 22, 0.3);
}

.dark .tab-button {
  color: #9ca3af;
}

.dark .tab-button:hover {
  background: rgba(249, 115, 22, 0.15);
  color: #fb923c;
}

.tab-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

@media (max-width: 640px) {
  .tab-button {
    justify-content: flex-start;
  }
}

// Tab Content
.tab-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.dark .tab-content {
  background: #1e293b;
}

.tab-panel {
  padding: 0;
}

// Error State
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #6b7280;
}

.dark .error-container {
  color: #9ca3af;
}

.error-icon {
  width: 4rem;
  height: 4rem;
  margin-bottom: 1rem;
  color: #ef4444;
}

.error-container h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.dark .error-container h3 {
  color: #d1d5db;
}

.error-container p {
  font-size: 0.875rem;
  max-width: 20rem;
  margin-bottom: 1.5rem;
}

.retry-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: linear-gradient(135deg, #ea580c, #dc2626);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.4);
}

// Responsive Design
@media (max-width: 1024px) {

  
  .user-page-content {
    padding: 0 0.5rem;
  }
}

@media (max-width: 768px) {
  .user-stats {
    justify-content: center;
  }
  
  .stat-item {
    text-align: center;
  }
}

// Legacy styles for backward compatibility
.tab-selected {
  background: rgba(249, 115, 22, 0.1);
  color: #f97316;
  cursor: pointer;
}

.dark .tab-selected {
  background: rgba(249, 115, 22, 0.15);
  color: #fb923c;
}
