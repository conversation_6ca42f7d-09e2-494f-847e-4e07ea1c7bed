import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { openDialog } from '@components/utils/animation';

@Component({
  selector: 'app-modal',
  imports: [CommonModule],
  templateUrl: './modal.component.html',
  styleUrl: './modal.component.scss',
  animations: [openDialog],
})
export class ModalComponent {
  constructor() { }
  @Input() content?: string;
  @Input() cancelText: string = 'Thoát';
  @Input() confirmText: string = 'Xác nhận';
  @Input() isVisible: boolean = false;
  @Input() title: string = 'Thông báo';

  @Output() cancel: EventEmitter<void> = new EventEmitter<void>();
  @Output() confirm?: EventEmitter<void> ;


  ngOnInit(): void {
    // Initialization logic here
  }

  onCancel(): void {
    this.isVisible = false; // Hide the modal when cancel is clicked
    this.cancel.emit();
  }
  onConfirm(): void {
    this.isVisible = false;
    this.confirm?.emit();
  }

  public openModal() {
    this.isVisible = true; // Show the modal when OpenModal is called
    this.confirm?.complete()
    this.confirm = new EventEmitter<void>();
  }

  // Add any additional methods or properties needed for the modal functionality

}
