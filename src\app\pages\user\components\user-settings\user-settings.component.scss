.settings-container {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

// Header
.settings-header {
  margin-bottom: 2rem;
  text-align: center;
}

.settings-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.dark .settings-title {
  color: #f9fafb;
}

.settings-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
}

.dark .settings-subtitle {
  color: #9ca3af;
}

// Layout
.settings-layout {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 2rem;
}

@media (max-width: 768px) {
  .settings-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

// Sidebar
.settings-sidebar {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 6px 1px rgba(95, 153, 174, 0.2);
  padding: 1rem;
  height: fit-content;
}

.dark .settings-sidebar {
  background: #262626;
  color: #e5e7eb;
}

.settings-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

@media (max-width: 768px) {
  .settings-nav {
    flex-direction: row;
    gap: 0.25rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  text-align: left;
  white-space: nowrap;
}

.nav-item:hover {
  background: rgba(249, 115, 22, 0.1);
  color: #f97316;
}

.nav-item.active {
  background: linear-gradient(135deg, #f97316, #ea580c);
  color: white;
}

.dark .nav-item {
  color: #9ca3af;
}

.dark .nav-item:hover {
  background: rgba(249, 115, 22, 0.15);
  color: #fb923c;
}

.nav-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

// Content
.settings-content {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 2px 6px 1px rgba(95, 153, 174, 0.2);
  padding: 2rem;
}

.dark .settings-content {
  background: #262626;
  color: #e5e7eb;
}

// Section
.settings-section {
  margin-bottom: 2rem;
}

.section-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.dark .section-header {
  border-bottom-color: #374151;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.dark .section-title {
  color: #f3f4f6;
}

.section-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
}

.dark .section-subtitle {
  color: #9ca3af;
}

// Form
.settings-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

@media (max-width: 640px) {
  .form-row {
    grid-template-columns: 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.dark .form-label {
  color: #d1d5db;
}

.form-input,
.form-select,
.form-textarea {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  background: white;
  color: #1f2937;
  transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #f97316;
  box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
}

.dark .form-input,
.dark .form-select,
.dark .form-textarea {
  background: #1f2937;
  border-color: #374151;
  color: #f3f4f6;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

// Toggle Switch
.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.dark .toggle-label {
  color: #d1d5db;
}

.toggle-input {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 3rem;
  height: 1.5rem;
  background: #d1d5db;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.toggle-slider::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 1.25rem;
  height: 1.25rem;
  background: white;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.toggle-input:checked + .toggle-slider {
  background: #f97316;
}

.toggle-input:checked + .toggle-slider::before {
  transform: translateX(1.5rem);
}

.dark .toggle-slider {
  background: #4b5563;
}

// Radio Group
.radio-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.radio-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  color: #374151;
}

.dark .radio-label {
  color: #d1d5db;
}

.radio-label input[type="radio"] {
  display: none;
}

.radio-custom {
  position: relative;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.radio-custom::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0.5rem;
  height: 0.5rem;
  background: #f97316;
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: all 0.3s ease;
}

.radio-label input[type="radio"]:checked + .radio-custom {
  border-color: #f97316;
}

.radio-label input[type="radio"]:checked + .radio-custom::before {
  transform: translate(-50%, -50%) scale(1);
}

.dark .radio-custom {
  border-color: #4b5563;
}

// Form Actions
.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

@media (max-width: 640px) {
  .form-actions {
    flex-direction: column;
  }
}

// Buttons
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}



.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

// Danger Zone
.danger-zone {
  margin-top: 3rem;
  padding: 1.5rem;
  border: 2px dashed #fca5a5;
  border-radius: 0.75rem;
  background: rgba(254, 226, 226, 0.5);
}

.dark .danger-zone {
  border-color: #7f1d1d;
  background: rgba(127, 29, 29, 0.1);
}

.danger-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 0.5rem;
}

.danger-subtitle {
  color: #7f1d1d;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.dark .danger-subtitle {
  color: #fca5a5;
}
