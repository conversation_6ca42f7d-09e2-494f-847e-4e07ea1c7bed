import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, ChangeDetectionStrategy, ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'app-seo-image',
  template: `
    <img
      [src]="src"
      [alt]="alt"
      [title]="title"
      [width]="width"
      [height]="height"
      [loading]="loading"
      [fetchpriority]="fetchpriority"
      [class]="cssClass"
      [style]="cssStyle"
      (error)="onImageError($event)"
      (load)="onImageLoad($event)"
    />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule]
})
export class SeoImageComponent implements OnInit {
  @Input() src!: string;
  @Input() alt!: string;
  @Input() title?: string;
  @Input() width?: number | string;
  @Input() height?: number | string;
  @Input() loading: 'lazy' | 'eager' = 'lazy';
  @Input() fetchpriority: 'high' | 'low' | 'auto' = 'auto';
  @Input() cssClass?: string;
  @Input() cssStyle?: string;
  @Input() fallbackSrc?: string = 'https://static.saytruyenhot.com/coverimg/ban-trai-toi-la-hoc-ba.jpg';

  ngOnInit() {
    // Set default title if not provided
    if (!this.title && this.alt) {
      this.title = this.alt;
    }
  }

  onImageError(event: Event) {
    const target = event.target as HTMLImageElement;
    if (this.fallbackSrc && target.src !== this.fallbackSrc) {
      target.src = this.fallbackSrc;
    }
  }

  onImageLoad(event: Event) {
    // Optional: Add loading success logic
    const target = event.target as HTMLImageElement;
    target.style.opacity = '1';
  }
}
