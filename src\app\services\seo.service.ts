import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Meta, MetaDefinition, Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Chapter } from '@schemas/Chapter';
import { Genre } from '@schemas/Genre';
import { Novel } from '@schemas/Novel';
import globalConfig from 'globalConfig';

export interface SEOData {
  title: string;
  description: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'book' | 'video';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  locale?: string;
  siteName?: string;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
  keywords?: string;
  category?: string;
}

@Injectable({
  providedIn: 'root',
})
export class SeoService {
  private readonly siteName = globalConfig.APP_NAME;
  private readonly defaultImage = '/logo.png';
  private readonly themeColor = '#ffffff';
  private readonly baseUrl = globalConfig.BASE_URL;

  constructor(
    private title: Title,
    private meta: Meta,
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) private platformId: object
  ) {
  }

  /**
   * Clear all dynamic meta tags before setting new ones
   */
  private clearDynamicMetaTags(): void {
    // Remove dynamic meta tags that should be updated per page
    const dynamicTags = [
      'description', 'author', 'robots', 'keywords', 'category',
      'og:title', 'og:description', 'og:type', 'og:url', 'og:image', 'og:site_name', 'og:locale',
      'twitter:title', 'twitter:description', 'twitter:url', 'twitter:image', 'twitter:card',
      'article:published_time', 'article:modified_time', 'article:section', 'article:tag'
    ];

    dynamicTags.forEach(tag => {
      if (tag.startsWith('og:') || tag.startsWith('twitter:') || tag.startsWith('article:')) {
        this.meta.removeTag(`property="${tag}"`);
      } else {
        this.meta.removeTag(`name="${tag}"`);
      }
    });
  }

  /**
   * Generate novel keywords for SEO
   */
  genNovelKeywords(novel: Novel, chapter?: Chapter): { title: string; href: string }[] {
    const novelUrl = this.getFullNovelUrl(novel);
    const keywords = [
      { title: `Truyện ${novel.title}`, href: novelUrl },
      { title: `Đọc truyện ${novel.title} online`, href: novelUrl },
      { title: `${novel.title} đầy đủ`, href: novelUrl },
      { title: `${novel.title} miễn phí`, href: novelUrl },
      { title: `Truyện chữ ${novel.title}`, href: novelUrl },
    ];

    if (chapter) {
      const chapterUrl = this.getFullChapterUrl(novel, chapter);
      keywords.push({ title: `${novel.title} chương ${chapter.slug}`, href: chapterUrl });
    }

    return keywords;
  }

  /**
   * Get full novel URL
   */
  getFullNovelUrl(novel: Novel): string {
    return `${this.baseUrl}/truyen/${novel.url}-${novel.id}`;
  }

  /**
   * Get full chapter URL
   */
  getFullChapterUrl(novel: Novel, chapter: Chapter): string {
    return `${this.baseUrl}/${novel.url}/chuong-${chapter.slug}/${chapter.id}`;
  }

  /**
   * Set comprehensive SEO data for a page
   */
  setSEOData(data: SEOData): void {
    // Clear existing dynamic meta tags first
    this.clearDynamicMetaTags();

    // Set title
    const fullTitle = data.title ? `${data.title} | ${this.siteName}` : this.siteName;
    this.title.setTitle(fullTitle);

    // Get current URL
    const currentUrl = data.url || `${this.baseUrl}${this.router.url}`;

    // Basic meta tags
    this.updateTag({ name: 'description', content: data.description });
    this.updateTag({ name: 'author', content: data.author || this.siteName });

    // Keywords meta tag
    const keywords = data.keywords || this.getDefaultKeywords();
    this.updateTag({ name: 'keywords', content: keywords });

    // Category meta tag
    if (data.category) {
      this.updateTag({ name: 'category', content: data.category });
    }

    // Robots meta
    const robotsContent = this.getRobotsContent(data.noindex, data.nofollow);
    this.updateTag({ name: 'robots', content: robotsContent });

    // Open Graph tags
    this.updateTag({ property: 'og:title', content: data.title || this.siteName });
    this.updateTag({ property: 'og:description', content: data.description });
    this.updateTag({ property: 'og:type', content: data.type || 'website' });
    this.updateTag({ property: 'og:url', content: currentUrl });
    this.updateTag({ property: 'og:image', content: data.image || `${this.baseUrl}${this.defaultImage}` });
    this.updateTag({ property: 'og:site_name', content: data.siteName || this.siteName });
    this.updateTag({ property: 'og:locale', content: data.locale || 'vi_VN' });

    // Article specific tags
    if (data.type === 'article' || data.type === 'book') {
      if (data.publishedTime) {
        this.updateTag({ property: 'article:published_time', content: data.publishedTime });
      }
      if (data.modifiedTime) {
        this.updateTag({ property: 'article:modified_time', content: data.modifiedTime });
      }
      if (data.section) {
        this.updateTag({ property: 'article:section', content: data.section });
      }
      if (data.tags) {
        data.tags.forEach(tag => {
          this.addTag({ property: 'article:tag', content: tag });
        });
      }
    }

    // Twitter Card tags
    this.updateTag({ name: 'twitter:card', content: data.twitterCard || 'summary_large_image' });
    this.updateTag({ name: 'twitter:title', content: data.title || this.siteName });
    this.updateTag({ name: 'twitter:description', content: data.description });
    this.updateTag({ name: 'twitter:image', content: data.image || `${this.baseUrl}${this.defaultImage}` });

    // Canonical URL
    this.updateCanonical(data.canonical || currentUrl);

    // Additional meta tags for better SEO
    this.updateTag({ name: 'theme-color', content: this.themeColor });
    this.updateTag({ name: 'msapplication-TileColor', content: this.themeColor });
  }

  /**
   * Add structured data (JSON-LD)
   */
  addStructuredData(schema: any): void {
    if (isPlatformBrowser(this.platformId)) {
      // Remove existing structured data
      this.removeAllStructuredData();

      // Handle both single schema and array of schemas
      const schemas = Array.isArray(schema) ? schema : [schema];

      schemas.forEach((singleSchema, index) => {
        const script = this.document.createElement('script');
        script.type = 'application/ld+json';
        script.text = JSON.stringify(singleSchema, null, 0); // Minified JSON
        script.id = `structured-data-${singleSchema['@type'] === 'Organization' ? 10+index : index}`;
        script.setAttribute('data-schema-type', singleSchema['@type'] || 'Unknown');
        this.document.head.appendChild(script);
      });
    }
  }

  /**
   * Remove all existing structured data
   */
  private removeAllStructuredData(): void {
    // Remove all structured data scripts
    const existingScripts = this.document.querySelectorAll('script[type="application/ld+json"]');
    existingScripts.forEach(script => {
      const type = script.getAttribute("data-schema-type");
      if(type === "Organization" || type === "WebSite") 
      return; // Skip organization and website schemas
      if ( (script.id.startsWith('structured-data-') || script.id === 'structured-data')) {
        script.remove();
      }
    });
  }

  /**
   * Set page title
   */
  setTitle(title: string): void {
    const fullTitle = title ? `${title} | ${this.siteName}` : this.siteName;
    this.title.setTitle(fullTitle);
  }

  /**
   * Add meta tags
   */
  addTags(tags: MetaDefinition[]): void {
    this.meta.addTags(tags);
  }

  /**
   * Add single meta tag
   */
  addTag(tag: MetaDefinition): void {
    this.meta.addTag(tag);
  }

  /**
   * Update meta tag
   */
  updateTag(tag: MetaDefinition): void {
    this.meta.updateTag(tag);
  }

  /**
   * Update canonical link
   */
  updateCanonical(url: string): void {
    this.updateLink('canonical', url);
  }

  /**
   * Update link element
   */
  updateLink(rel: string, href: string): void {
    const head = this.document.head;
    let element: HTMLLinkElement | null = this.document.querySelector(`link[rel='${rel}']`);

    if (!element) {
      element = this.document.createElement('link') as HTMLLinkElement;
      head.appendChild(element);
    }

    element.setAttribute('rel', rel);
    element.setAttribute('href', href);
  }



  private getDefaultKeywords(): string {
    return 'truyện chữ, đọc truyện online, truyện hay, truyện full, ngôn tình, tiên hiệp, kiếm hiệp, đô thị, huyền huyễn';
  }

  /**
   * Set SEO for genre/category page
   */
  setGenreSEO(genre: Genre, novels?: Novel[], page: number = 1): void {
    const title = page > 1
      ? `Truyện chữ ${genre.title} - Trang ${page}`
      : `Truyện chữ ${genre.title}`;

    const description = `Đọc truyện chữ thể loại ${genre.title} online miễn phí. ${novels?.length || 0} bộ truyện chất lượng cao, cập nhật liên tục tại ${this.siteName}.`;
    const keywords = `${genre.title}, truyện chữ ${genre.title}, ${this.getDefaultKeywords()}`;

    const seoData: SEOData = {
      title,
      description,
      type: 'website',
      siteName: this.siteName,
      twitterCard: 'summary',
      keywords,
      category: genre.title,
      canonical: page > 1 ? undefined : `${this.baseUrl}/the-loai/${genre.slug}`
    };

    this.setSEOData(seoData);

    // Add structured data
    const schemas = [
      this.generateGenreSchema(genre, novels || []),
      this.generateBreadcrumbSchema([
        { name: 'Trang chủ', url: '/' },
        { name: `Thể loại ${genre.title}`, url: `/the-loai/${genre.slug}` }
      ]),
      this.generateWebsiteSchema()
    ];

    this.addStructuredData(schemas);
  }

  addCommonSchema(): void {
    const organizationSchema = this.generateOrganizationSchema();
    const websiteSchema = this.generateWebsiteSchema();
    this.addStructuredData([organizationSchema, websiteSchema]);

  }
  /**
   * Generate genre page structured data
   */
  private generateGenreSchema(genre: Genre, novels: Novel[]): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      'name': `Truyện chữ ${genre.title}`,
      'description': genre.description || `Kho tàng truyện chữ thể loại ${genre.title}`,
      'url': `${this.baseUrl}/the-loai/${genre.slug}`,
      'mainEntity': {
        '@type': 'ItemList',
        'name': `Danh sách truyện ${genre.title}`,
        'numberOfItems': novels.length,
        'itemListElement': novels.slice(0, 10).map((novel, index) => ({
          '@type': 'ListItem',
          'position': index + 1,
          'item': {
            '@type': 'Book',
            'name': novel.title,
            'url': this.getFullNovelUrl(novel),
            'image': novel.coverImage,
            'author': novel.author,
            'genre': genre.title
          }
        }))
      },
      'breadcrumb': {
        '@type': 'BreadcrumbList',
        'itemListElement': [
          {
            '@type': 'ListItem',
            'position': 1,
            'name': 'Trang chủ',
            'item': this.baseUrl
          },
          {
            '@type': 'ListItem',
            'position': 2,
            'name': `Thể loại ${genre.title}`,
            'item': `${this.baseUrl}/the-loai/${genre.slug}`
          }
        ]
      }
    };
  }

  /**
   * Generate consistent author object for E-A-T signals
   */
  generateAuthorObject(authorName?: string): any {
    if (authorName && authorName.trim()) {
      return {
        '@type': 'Person',
        'name': authorName.trim()
      };
    }
    return undefined;
  }

  /**
   * Get robots content
   */
  private getRobotsContent(noindex?: boolean, nofollow?: boolean): string {
    const index = noindex ? 'noindex' : 'index';
    const follow = nofollow ? 'nofollow' : 'follow';
    return `${index}, ${follow}`;
  }

  /**
   * Generate breadcrumb structured data
   */
  generateBreadcrumbSchema(breadcrumbs: Array<{ name: string, url: string }>): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': breadcrumbs.map((item, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'name': item.name,
        'item': `${this.baseUrl}${item.url}`
      }))
    };
  }

  /**
   * Generate website structured data
   */
  generateWebsiteSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      'name': this.siteName,
      'url': this.baseUrl,
      'description': 'Website đọc truyện chữ online miễn phí với kho tàng truyện chữ phong phú, cập nhật liên tục',
      'potentialAction': {
        '@type': 'SearchAction',
        'target': `${this.baseUrl}/tim-kiem?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      },
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.baseUrl,
        'logo': {
          '@type': 'ImageObject',
          'url': `${this.baseUrl}/logo.png`,
          'width': 480,
          'height': 480
        }
      }
    };
  }

  /**
   * Generate novel book structured data
   */
  generateNovelSchema(novel: Novel): any {
    const schema: any = {
      '@context': 'https://schema.org',
      '@type': 'Book',
      'name': novel.title,
      'description': `${novel.title}, ${novel.author}, đọc truyện ${novel.title}, truyện ${novel.genres.map(v => v.title).join(', ')}, truyện hay, truyện online, đọc truyện miễn phí, ${globalConfig.APP_NAME}`,
      'image': novel.coverImage || `${this.baseUrl}${this.defaultImage}`,
      'url': this.getFullNovelUrl(novel),
      'identifier': {
        '@type': 'PropertyValue',
        'name': 'Novel ID',
        'value': novel.id.toString()
      },
      'author': this.generateAuthorObject(novel.author),
      'genre': novel.genres?.map((g) => g.title) || [],
      'bookFormat': 'EBook',
      'inLanguage': 'vi',
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.baseUrl
      },
      'datePublished': novel.updateAt,
      'dateModified': novel.updateAt,
      'numberOfPages': novel.numChapter,
      'workExample': novel.chapters?.slice(0, 5).map((chapter: Chapter) => ({
        '@type': 'Chapter',
        'name': chapter.title || `Chương ${chapter.slug}`,
        'url': this.getFullChapterUrl(novel, chapter),
        'position': chapter.slug || chapter.id
      })) || [],
      'audience': {
        '@type': 'Audience',
        'audienceType': 'Novel Readers'
      },
      'keywords': this.generateNovelKeywordsString(novel),
      'wordCount': novel.wordCount || undefined,
    };

    return schema;
  }

  /**
   * Generate content rating based on genres
   */
  private getContentRating(genres: Genre[]): string {
    if (!genres) return 'General';
    const genreNames = genres.map(g => (g.title).toLowerCase());
    if (genreNames.some(name => ['adult', 'smut', 'mature', 'ecchi', '18+'].includes(name))) {
      return 'Mature';
    }
    if (genreNames.some(name => ['romance', 'drama', 'psychological'].includes(name))) {
      return 'Teen';
    }
    return 'General';
  }

  /**
   * Generate keywords string for novel
   */
  private generateNovelKeywordsString(novel: Novel): string {
    const keywords = [
      novel.title,
      `truyện ${novel.title}`,
      'truyện chữ online',
      'đọc truyện miễn phí'
    ];

    if (novel.genres) {
      keywords.push(...novel.genres.map((g) => g.title));
    }

    if (novel.author) {
      keywords.push(novel.author);
    }

    return keywords.filter(Boolean).join(', ');
  }

  /**
   * Generate article structured data for chapters
   */
  generateChapterSchema(novel: Novel, chapter: Chapter): any {
    const chapterUrl = this.getFullChapterUrl(novel, chapter);
    const chapterTitle = `Chương ${chapter.slug}`;
    const description = `${chapterTitle}: Đọc truyện "${novel.title}"${novel.author ? ' của ' + novel.author : ''}. Nội dung mới nhất, chất lượng, cập nhật liên tục tại ${globalConfig.APP_NAME}.`;
    return {
      '@context': 'https://schema.org',
      '@type': 'Article',
      'headline': `${novel.title} ${chapterTitle}`,
      'alternativeHeadline': `Đọc ${novel.title} ${chapterTitle} Online Miễn Phí`,
      'description': description,
      'image': novel.coverImage || `${this.baseUrl}${this.defaultImage}`,
      'url': chapterUrl,
      'datePublished': chapter.updateAt,
      'dateModified': chapter.updateAt,
      'author': this.generateAuthorObject(novel.author),
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.baseUrl,
        'logo': {
          '@type': 'ImageObject',
          'url': `${this.baseUrl}/logo.png`,
          'width': 480,
          'height': 480
        }
      },
      'mainEntityOfPage': {
        '@type': 'WebPage',
        '@id': chapterUrl
      },
      'isPartOf': {
        '@type': 'Book',
        'name': novel.title,
        'url': this.getFullNovelUrl(novel)
      },
      'position': chapter.slug,
      'inLanguage': 'vi',
      'genre': novel.genres?.map(g => g.title) || [],
      'keywords': this.generateChapterKeywords(novel, chapter)
    };
  }

  /**
   * Generate chapter-specific keywords
   */
  private generateChapterKeywords(novel: Novel, chapter: Chapter): string {
    const chapterName = chapter.title || `Chương ${chapter.slug}`;
    const keywords = [
      novel.title,
      chapterName,
      `${novel.title} ${chapterName}`,
      'đọc truyện online',
      'truyện chữ miễn phí'
    ];

    if (novel.genres) {
      keywords.push(...novel.genres.map(g => g.title));
    }

    if (novel.author) {
      keywords.push(novel.author);
    }

    return keywords.filter(Boolean).join(', ');
  }

  /**
   * Generate ItemList schema for novel listings
   */
  generateNovelListSchema(novels: Novel[], listName: string, listDescription?: string): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'ItemList',
      'name': listName,
      'description': listDescription || `Danh sách ${listName.toLowerCase()} tại ${this.siteName}`,
      'numberOfItems': novels.length,
      'itemListOrder': 'https://schema.org/ItemListOrderDescending',
      'itemListElement': novels.map((novel, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'item': {
          '@type': 'Book',
          'name': novel.title,
          'url': this.getFullNovelUrl(novel),
          'image': novel.coverImage,
          'author': this.generateAuthorObject(novel.author),
          'genre': novel.genres?.map((g) => g.title) || [],
          'dateModified': novel.updateAt,
        }
      }))
    };
  }

  /**
   * Generate FAQ schema for novel pages
   */
  generateNovelFAQSchema(novel: Novel): any {
    const latestChapter = novel.chapters?.[0]?.slug;
    const status = novel.status === 0 ? 'Đang cập nhật' : 'Hoàn thành';
    const totalChapters = novel.numChapter;

    return {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      'mainEntity': [
        {
          '@type': 'Question',
          'name': `${novel.title} có bao nhiêu chương?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `${novel.title} hiện tại có ${totalChapters} chương, chương mới nhất là chương ${latestChapter}.`
          }
        },
        {
          '@type': 'Question',
          'name': `Tình trạng của ${novel.title} như thế nào?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `${novel.title} hiện tại đang ở trạng thái ${status}.`
          }
        },
        {
          '@type': 'Question',
          'name': `Đọc ${novel.title} ở đâu miễn phí?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `Bạn có thể đọc ${novel.title} miễn phí tại ${this.siteName} với chất lượng cao và cập nhật nhanh nhất.`
          }
        },
        {
          '@type': 'Question',
          'name': `${novel.title} thuộc thể loại gì?`,
          'acceptedAnswer': {
            '@type': 'Answer',
            'text': `${novel.title} thuộc thể loại ${novel.genres?.map((g) => g.title).join(', ') || 'Đang cập nhật'}.`
          }
        }
      ]
    };
  }

  /**
   * Generate Organization schema for the website
   */
  generateOrganizationSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      'name': this.siteName,
      'url': this.baseUrl,
      'logo': {
        '@type': 'ImageObject',
        'url': `${this.baseUrl}/logo.png`,
        'width': 480,
        'height': 480
      },
      'description': 'Website đọc truyện chữ online hàng đầu Việt Nam với kho tàng truyện chữ phong phú',
      'foundingDate': '2025',
      'contactPoint': {
        '@type': 'ContactPoint',
        'contactType': 'customer service',
        'availableLanguage': 'Vietnamese'
      },
      'areaServed': 'VN',
      'serviceType': 'Online Novel Reading Platform'
    };
  }


}
