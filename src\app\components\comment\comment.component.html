<div
  class="lg:container flex flex-col w-full p-4 rounded-lg mx-auto"
>
  <div
    class="mb-4 w-full flex flex-row flex-wrap justify-between items-end border-b border-neutral-300"
  >
    <div
      class="border-b-2 -mb-[1.2px] h-full border-primary-100 text-xl font-medium"
      data-comments-count="2810"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="size-5 inline-block mr-2"
        viewBox="0 0 21 21"
        fill="none"
      >
        <g clip-path="url(#clip0_281_3026)">
          <path
            d="M14.499 0.5H6.50109C3.19363 0.5 0.502686 3.19095 0.502686 6.4984V11.1638C0.502686 14.3596 3.01468 16.9796 6.16784 17.1532V19.9338C6.16784 20.2461 6.42244 20.5 6.73536 20.5C6.88498 20.5 7.02661 20.4407 7.13358 20.3337L7.75875 19.7085C9.40031 18.0666 11.5834 17.1622 13.9054 17.1622H14.499C17.8064 17.1622 20.4974 14.4713 20.4974 11.1638V6.4984C20.4974 3.19095 17.8064 0.5 14.499 0.5ZM6.16784 10.1641C5.4327 10.1641 4.83486 9.56625 4.83486 8.83111C4.83486 8.09597 5.4327 7.49813 6.16784 7.49813C6.90298 7.49813 7.50082 8.09597 7.50082 8.83111C7.50082 9.56625 6.90265 10.1641 6.16784 10.1641ZM10.5 10.1641C9.76488 10.1641 9.16704 9.56625 9.16704 8.83111C9.16704 8.09597 9.76488 7.49813 10.5 7.49813C11.2352 7.49813 11.833 8.09597 11.833 8.83111C11.833 9.56625 11.2348 10.1641 10.5 10.1641ZM14.8322 10.1641C14.0971 10.1641 13.4992 9.56625 13.4992 8.83111C13.4992 8.09597 14.0971 7.49813 14.8322 7.49813C15.5673 7.49813 16.1652 8.09597 16.1652 8.83111C16.1652 9.56625 15.567 10.1641 14.8322 10.1641Z"
            fill="currentColor"
          ></path>
        </g>
      </svg>
      Bình luận (<span class="text-lg font-medium" title="2810">{{
        lenghtComments
      }}</span
      >)
    </div>
  </div>
  <div class="flex flex-col gap-2 text-sm">
    @if(isLogin) { Hãy để lại một bình luận nhé! } @else { 
      <p>
        Vui lòng <b class="text-primary-100 cursor-pointer"> đăng nhập </b> để tham gia bình luận. 
      </p>
    }
    <form
      (submit)="onSubmit(formComment)"
      [formGroup]="formComment"
      class="w-full flex flex-col gap-2 mt-2 p-2 bg-neutral-200/50 dark:bg-dark-700 rounded-xl"
    >
    <textarea
      class="text-sm bg-white border-common dark:bg-dark-background rounded-xl leading-normal resize-none w-full h-24 py-2 px-3 font-medium placeholder-gray-400 focus:outline-none focus:border border-gray-300 dark:focus:bg-neutral-700"
      name="content"
      placeholder="Viết bình luận..."
      required
      formControlName="content"
      (input)="onInput(formComment)">
    </textarea>

      <div class="w-full flex justify-end">
        <button
          class="text-dark-text font-semibold disabled:bg-neutral-300 dark:disabled:bg-neutral-700 dark:disabled:text-neutral-400 bg-red-500 cursor-pointer px-4 py-1.5 hover:bg-red-600 rounded-full inline-flex-center gap-1"
          type="submit"
          title="Bình luận"
        >
          Bình luận
        </button>
      </div>
    </form>
    <!-- <div
      class="mb-4 mt-4 w-full flex-center gap-2 p-2 bg-neutral-200/80 dark:bg-dark-700 rounded-xl h-32"
    >
    <p class="text-sm ">Chưa có bình luận nào</p>
  </div> -->
    <div
      [@inOutAnimation]="'in'"
      *ngFor="let comment of listComments"
      class="flex flex-col gap-1 transition-all"
    >
      <ng-container
        [ngTemplateOutlet]="commentBlockTemplate"
        [ngTemplateOutletContext]="{ comment: comment, hide: false }"
      >
      </ng-container>

      <div
        #ViewReplyEle
        [attr.reply-block]="comment.id"
        class="pl-[52px] md:pl-[72px] gap-1 flex flex-col overflow-hidden h-0"
        [ngClass]="{
          'overflow-visible': replyId === comment.id && !isViewreply
        }"
      >
        <ng-container
          *ngFor="let reply of comment.replies"
          [ngTemplateOutlet]="commentBlockTemplate"
          [ngTemplateOutletContext]="{ comment: reply, hide: true }"
        >
        </ng-container>

        @if (isLogin) {
        <form
          (submit)="onSubmit(formReply, comment.id)"
          [formGroup]="formReply"
          *ngIf="replyId === comment.id"
        >
          <div class="w-full flex-start mt-3">
            <div class="px-3">
              <div class="flex gap-3 items-center size-12">
                <img
                  loading="lazy"
                  downloda="tai xuong.jpg"
                  [src]="user?.avatar"
                  onerror="this.src='/logo.png'"
                  class="object-cover p-0.5 size-12 rounded-full"
                  alt="avatar"
                />
              </div>
            </div>
            <textarea
              class="dark:bg-dark-bg focus:border border-gray-300 dark:focus:bg-neutral-700 text-sm rounded-lg leading-normal resize-none w-full h-12 py-2 px-3 focus:outline-none focus:bg-white"
              name="content"
              placeholder="Viết bình luận..."
              required
              formControlName="content"
            ></textarea>
          </div>

          <div class="w-full flex justify-end mt-2">
            <button
              class="w-24 h-8 font-medium text-white bg-primary-100 hover:bg-primary-200 rounded-full"
              type="submit"
              title="Bình luận"
            >
              Bình luận
            </button>
          </div>
        </form>
        }
      </div>
      <!-- Reply Container  -->

      <!-- Reply Container  -->
    </div>
    <div class="text-gray-300 font-bold pl-14 h-2"></div>

    <!-- Reply Container  -->
    <div class="text-gray-300 font-bold pl-14 h-2"></div>
  </div>

  <!-- <app-pagination
    *ngIf="totalpage > 1"
    [currentPage]="currentPage"
    [totalpage]="totalpage"
    (OnChange)="OnChangePage($event)"
  >
  </app-pagination> -->
</div>

<ng-template #commentBlockTemplate let-comment="comment" let-hide="hide">
  <div class="comment-block" [id]="'id' + comment.id">
    <div class="comment-avatar">
      <div (click)="ViewInfoUser(comment.userID)" class="comment-avatar-icon">
        <img
          loading="lazy"
          download="tai xuong.jpg"
          [src]="comment?.avatar"
          class="comment-avatar-img"
          onerror="this.src='/logo.png'"
          alt="avatar"
        />
      </div>
    </div>
    <div class="comment-content">
      <div class="comment-header border-b-2">
        <p class="comment-username">{{ comment.userName }}</p>
        <span *ngIf="hide === false">
          <span class="comment-date"
            >({{ comment.commentedAt | dateAgo }})</span
          >
        </span>
        <span class="flex-1"></span>
        <button
          class="comment-reply"
          (click)="replyCmt(comment, hide)"
          title="Trả lời"
        >
          Trả lời
        </button>
      </div>
      <div class="comment-text" [innerHTML]="comment.content">
        <!-- {{ comment.content | emojiParser }} -->
      </div>
      <div *ngIf="comment.replies && comment.replies.length > 0">
        <button
          title="xem phản hồi"
          class="comment-view-reply"
          (click)="ViewReplyCmt(comment.id)"
        >
          Xem ({{ comment.replies.length }}) phản hồi
        </button>
      </div>
    </div>
  </div>
</ng-template>
