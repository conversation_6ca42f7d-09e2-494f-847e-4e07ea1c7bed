<div
  #VirtualScroll
  class="w-full overflow-y-auto overflow-x-hidden h-full relative scrollbar-style-md"
>
  <div #VirtualScrollSpace class="absolute top-0 w-1 -z-50 left-0"></div>
  <div #VirtualScrollContent class="grid grid-cols-{{gridSize}}">
    <ng-container *ngFor="let item of preLoadItems">
        <ng-template
            [ngTemplateOutlet]="itemTemplateRef"
            [ngTemplateOutletContext]="{ $implicit: item, item : item }"
        >
        </ng-template>
    </ng-container>
  </div>
</div>
<ng-content />

