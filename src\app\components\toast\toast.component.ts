// src/app/toast/toast.component.ts
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import { Component, ViewEncapsulation } from '@angular/core';
import { ToastService } from '@services/toast.service';


@Component({
  selector: 'app-toast',
  templateUrl: './toast.component.html',
  styleUrls: ['./toast.component.scss'],
  imports: [CommonModule],
  encapsulation: ViewEncapsulation.None,
  animations: [
    trigger('toastAnimation', [
      state(
        'void',
        style({
          opacity: 0,
          transform: 'translateY(100%)',
        })
      ),
      state(
        'enter',
        style({
          opacity: 1,
          transform: 'translateY(0)',
        })
      ),
      state(
        'leave',
        style({
          opacity: 0,
          height: 0,
        })
      ),
      transition('void => enter', [animate('300ms ease-in')]),
      transition('enter => leave', [animate('300ms ease-out')]),
    ]),
  ],
})
export class ToastComponent {
  constructor(public toastService: ToastService) { }

}
