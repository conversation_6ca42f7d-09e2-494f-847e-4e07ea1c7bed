.toast-container {
    @apply z-[1055] fixed top-0 end-0 py-4;
  }

  .toast {
    @apply transition-all overflow-hidden rounded-l-md my-2 border-y-[1px] border-l-[1px] border-r-4;
  }

  .toast-background {
    @apply h-52 -z-10 backdrop-blur-xl absolute opacity-85 items-center shadow-md w-full;
  }

  .toast-background-success {
    @apply bg-green-200;
  }

  .toast-background-error {
    @apply bg-red-200;
  }

  .toast-background-warning {
    @apply bg-yellow-200;
  }

  .toast-background-info {
    @apply bg-blue-200;
  }

  .toast-content {
    @apply min-w-72 py-2 px-3 items-center flex;
  }

  .toast-icon {
    @apply rounded-full bg-white mr-3;
  }

  .toast-icon-success {
    @apply text-green-500;
  }

  .toast-icon-error {
    @apply text-red-500;
  }

  .toast-icon-warning {
    @apply text-yellow-500;
  }

  .toast-icon-info {
    @apply text-blue-500;
  }

  .toast-message {
    @apply max-w-xs;
  }

  .toast-message-success {
    @apply text-green-700;
  }

  .toast-message-error {
    @apply text-red-700;
  }

  .toast-message-warning {
    @apply text-yellow-700;
  }

  .toast-message-info {
    @apply text-blue-700;
  }